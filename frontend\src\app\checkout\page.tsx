"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  CreditCard,
  MapPin,
  User,
  Phone,
  Mail,
  Calendar,
  Shield,
  Truck,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  ArrowRight
} from 'lucide-react'

// أنواع البيانات
interface ShippingAddress {
  fullName: string
  phone: string
  email: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
}

interface PaymentMethod {
  type: 'card' | 'cash' | 'bank_transfer'
  cardNumber?: string
  expiryDate?: string
  cvv?: string
  cardholderName?: string
}

export default function CheckoutPage() {
  const { user, profile } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    fullName: profile?.full_name || '',
    phone: profile?.phone || '',
    email: profile?.email || '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'الإمارات العربية المتحدة'
  })
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>({
    type: 'card'
  })
  const [deliveryOption, setDeliveryOption] = useState('standard')
  const [specialInstructions, setSpecialInstructions] = useState('')
  const [agreeToTerms, setAgreeToTerms] = useState(false)
  const [loading, setLoading] = useState(false)

  // بيانات الطلب (محاكاة)
  const orderSummary = {
    items: [
      { name: 'زي التخرج الكلاسيكي', quantity: 1, price: 299.99 },
      { name: 'قبعة التخرج المميزة', quantity: 1, price: 89.99 }
    ],
    subtotal: 389.98,
    discount: 0,
    shipping: 25,
    tax: 20.75,
    total: 435.73
  }

  const deliveryOptions = [
    {
      id: 'standard',
      name: 'التوصيل العادي',
      description: '3-5 أيام عمل',
      price: 25,
      icon: <Truck className="h-5 w-5" />
    },
    {
      id: 'express',
      name: 'التوصيل السريع',
      description: '1-2 أيام عمل',
      price: 50,
      icon: <Truck className="h-5 w-5" />
    },
    {
      id: 'same_day',
      name: 'التوصيل في نفس اليوم',
      description: 'خلال 6 ساعات',
      price: 100,
      icon: <Truck className="h-5 w-5" />
    }
  ]

  const paymentMethods = [
    {
      id: 'card',
      name: 'بطاقة ائتمان/خصم',
      description: 'Visa, Mastercard, American Express',
      icon: <CreditCard className="h-5 w-5" />
    },
    {
      id: 'cash',
      name: 'الدفع عند الاستلام',
      description: 'ادفع نقداً عند وصول الطلب',
      icon: <CheckCircle className="h-5 w-5" />
    },
    {
      id: 'bank_transfer',
      name: 'تحويل بنكي',
      description: 'تحويل مباشر إلى حساب البنك',
      icon: <Shield className="h-5 w-5" />
    }
  ]

  const handleSubmitOrder = async () => {
    if (!agreeToTerms) {
      alert('يرجى الموافقة على الشروط والأحكام')
      return
    }

    setLoading(true)
    
    // محاكاة معالجة الطلب
    setTimeout(() => {
      setLoading(false)
      // توجيه إلى صفحة تأكيد الطلب
      window.location.href = '/order-confirmation'
    }, 2000)
  }

  const steps = [
    { id: 1, name: 'معلومات الشحن', icon: <MapPin className="h-4 w-4" /> },
    { id: 2, name: 'طريقة التوصيل', icon: <Truck className="h-4 w-4" /> },
    { id: 3, name: 'طريقة الدفع', icon: <CreditCard className="h-4 w-4" /> },
    { id: 4, name: 'مراجعة الطلب', icon: <CheckCircle className="h-4 w-4" /> }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            إتمام الطلب 💳
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            أكمل معلوماتك لإتمام عملية الشراء
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    step.icon
                  )}
                </div>
                <span className={`ml-3 text-sm font-medium arabic-text ${
                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                }`}>
                  {step.name}
                </span>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Step 1: Shipping Information */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <MapPin className="h-5 w-5" />
                    معلومات الشحن
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    أدخل عنوان التوصيل ومعلومات الاتصال
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="fullName" className="arabic-text">الاسم الكامل</Label>
                      <Input
                        id="fullName"
                        value={shippingAddress.fullName}
                        onChange={(e) => setShippingAddress(prev => ({
                          ...prev,
                          fullName: e.target.value
                        }))}
                        className="arabic-text"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone" className="arabic-text">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        value={shippingAddress.phone}
                        onChange={(e) => setShippingAddress(prev => ({
                          ...prev,
                          phone: e.target.value
                        }))}
                        className="arabic-text"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email" className="arabic-text">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={shippingAddress.email}
                      onChange={(e) => setShippingAddress(prev => ({
                        ...prev,
                        email: e.target.value
                      }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="address" className="arabic-text">العنوان التفصيلي</Label>
                    <Textarea
                      id="address"
                      value={shippingAddress.address}
                      onChange={(e) => setShippingAddress(prev => ({
                        ...prev,
                        address: e.target.value
                      }))}
                      placeholder="رقم المبنى، اسم الشارع، المنطقة..."
                      className="arabic-text"
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="city" className="arabic-text">المدينة</Label>
                      <Input
                        id="city"
                        value={shippingAddress.city}
                        onChange={(e) => setShippingAddress(prev => ({
                          ...prev,
                          city: e.target.value
                        }))}
                        className="arabic-text"
                      />
                    </div>
                    <div>
                      <Label htmlFor="state" className="arabic-text">الإمارة</Label>
                      <Input
                        id="state"
                        value={shippingAddress.state}
                        onChange={(e) => setShippingAddress(prev => ({
                          ...prev,
                          state: e.target.value
                        }))}
                        className="arabic-text"
                      />
                    </div>
                    <div>
                      <Label htmlFor="zipCode" className="arabic-text">الرمز البريدي</Label>
                      <Input
                        id="zipCode"
                        value={shippingAddress.zipCode}
                        onChange={(e) => setShippingAddress(prev => ({
                          ...prev,
                          zipCode: e.target.value
                        }))}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={() => setCurrentStep(2)} className="arabic-text">
                      التالي
                      <ArrowLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Delivery Options */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <Truck className="h-5 w-5" />
                    طريقة التوصيل
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    اختر طريقة التوصيل المناسبة لك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={deliveryOption} onValueChange={setDeliveryOption}>
                    <div className="space-y-4">
                      {deliveryOptions.map((option) => (
                        <div key={option.id} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.id} id={option.id} />
                          <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                            <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                              <div className="flex items-center gap-3">
                                {option.icon}
                                <div>
                                  <p className="font-medium arabic-text">{option.name}</p>
                                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                                    {option.description}
                                  </p>
                                </div>
                              </div>
                              <span className="font-bold">{option.price} درهم</span>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </RadioGroup>

                  <div className="mt-6">
                    <Label htmlFor="instructions" className="arabic-text">تعليمات خاصة (اختياري)</Label>
                    <Textarea
                      id="instructions"
                      value={specialInstructions}
                      onChange={(e) => setSpecialInstructions(e.target.value)}
                      placeholder="أي تعليمات خاصة للتوصيل..."
                      className="arabic-text"
                    />
                  </div>

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(1)} className="arabic-text">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <Button onClick={() => setCurrentStep(3)} className="arabic-text">
                      التالي
                      <ArrowLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Payment Method */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <CreditCard className="h-5 w-5" />
                    طريقة الدفع
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    اختر طريقة الدفع المفضلة لديك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={paymentMethod.type} onValueChange={(value) => 
                    setPaymentMethod(prev => ({ ...prev, type: value as any }))
                  }>
                    <div className="space-y-4">
                      {paymentMethods.map((method) => (
                        <div key={method.id} className="flex items-center space-x-2">
                          <RadioGroupItem value={method.id} id={method.id} />
                          <Label htmlFor={method.id} className="flex-1 cursor-pointer">
                            <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                              {method.icon}
                              <div>
                                <p className="font-medium arabic-text">{method.name}</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                                  {method.description}
                                </p>
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </RadioGroup>

                  {/* Credit Card Form */}
                  {paymentMethod.type === 'card' && (
                    <div className="mt-6 space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
                      <h3 className="font-medium arabic-text">معلومات البطاقة</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="col-span-2">
                          <Label htmlFor="cardNumber" className="arabic-text">رقم البطاقة</Label>
                          <Input
                            id="cardNumber"
                            placeholder="1234 5678 9012 3456"
                            value={paymentMethod.cardNumber || ''}
                            onChange={(e) => setPaymentMethod(prev => ({
                              ...prev,
                              cardNumber: e.target.value
                            }))}
                          />
                        </div>
                        <div>
                          <Label htmlFor="expiryDate" className="arabic-text">تاريخ الانتهاء</Label>
                          <Input
                            id="expiryDate"
                            placeholder="MM/YY"
                            value={paymentMethod.expiryDate || ''}
                            onChange={(e) => setPaymentMethod(prev => ({
                              ...prev,
                              expiryDate: e.target.value
                            }))}
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvv" className="arabic-text">CVV</Label>
                          <Input
                            id="cvv"
                            placeholder="123"
                            value={paymentMethod.cvv || ''}
                            onChange={(e) => setPaymentMethod(prev => ({
                              ...prev,
                              cvv: e.target.value
                            }))}
                          />
                        </div>
                        <div className="col-span-2">
                          <Label htmlFor="cardholderName" className="arabic-text">اسم حامل البطاقة</Label>
                          <Input
                            id="cardholderName"
                            value={paymentMethod.cardholderName || ''}
                            onChange={(e) => setPaymentMethod(prev => ({
                              ...prev,
                              cardholderName: e.target.value
                            }))}
                            className="arabic-text"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(2)} className="arabic-text">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <Button onClick={() => setCurrentStep(4)} className="arabic-text">
                      التالي
                      <ArrowLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4: Order Review */}
            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <CheckCircle className="h-5 w-5" />
                    مراجعة الطلب
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    راجع تفاصيل طلبك قبل التأكيد
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Order Items */}
                  <div>
                    <h3 className="font-medium mb-3 arabic-text">المنتجات</h3>
                    <div className="space-y-2">
                      {orderSummary.items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <span className="arabic-text">{item.name} × {item.quantity}</span>
                          <span>{item.price} درهم</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div>
                    <h3 className="font-medium mb-3 arabic-text">عنوان التوصيل</h3>
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <p className="arabic-text">{shippingAddress.fullName}</p>
                      <p className="arabic-text">{shippingAddress.address}</p>
                      <p className="arabic-text">{shippingAddress.city}, {shippingAddress.state} {shippingAddress.zipCode}</p>
                      <p>{shippingAddress.phone}</p>
                    </div>
                  </div>

                  {/* Terms and Conditions */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="terms"
                      checked={agreeToTerms}
                      onCheckedChange={setAgreeToTerms}
                    />
                    <Label htmlFor="terms" className="text-sm arabic-text">
                      أوافق على <a href="/terms" className="text-blue-600 hover:underline">الشروط والأحكام</a> و
                      <a href="/privacy" className="text-blue-600 hover:underline">سياسة الخصوصية</a>
                    </Label>
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)} className="arabic-text">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <Button 
                      onClick={handleSubmitOrder} 
                      disabled={!agreeToTerms || loading}
                      className="arabic-text"
                    >
                      {loading ? 'جاري المعالجة...' : 'تأكيد الطلب'}
                      <CheckCircle className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle className="arabic-text">ملخص الطلب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  {orderSummary.items.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="arabic-text">{item.name} × {item.quantity}</span>
                      <span>{item.price} درهم</span>
                    </div>
                  ))}
                </div>

                <Separator />

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="arabic-text">المجموع الفرعي:</span>
                    <span>{orderSummary.subtotal} درهم</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="arabic-text">الشحن:</span>
                    <span>{orderSummary.shipping} درهم</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="arabic-text">الضريبة:</span>
                    <span>{orderSummary.tax} درهم</span>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-between font-bold text-lg">
                  <span className="arabic-text">الإجمالي:</span>
                  <span>{orderSummary.total} درهم</span>
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Shield className="h-4 w-4" />
                  <span className="arabic-text">دفع آمن ومضمون</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
