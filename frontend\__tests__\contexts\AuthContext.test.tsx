import { renderHook, act } from '@testing-library/react'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import { createClient } from '@supabase/supabase-js'

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } }
      }))
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn()
        }))
      })),
      insert: jest.fn(),
      update: jest.fn(() => ({
        eq: jest.fn()
      }))
    }))
  }))
}))

const mockSupabase = createClient('', '')

// Test wrapper
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
)

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('provides initial auth state', () => {
    const { result } = renderHook(() => useAuth(), { wrapper })

    expect(result.current.user).toBeNull()
    expect(result.current.profile).toBeNull()
    expect(result.current.loading).toBe(true)
  })

  test('handles user sign up', async () => {
    const mockSignUp = jest.fn().mockResolvedValue({
      data: {
        user: {
          id: '1',
          email: '<EMAIL>'
        }
      },
      error: null
    })

    ;(mockSupabase.auth.signUp as jest.Mock) = mockSignUp

    const { result } = renderHook(() => useAuth(), { wrapper })

    await act(async () => {
      await result.current.signUp('<EMAIL>', 'password123', {
        full_name: 'Test User',
        role: 'student'
      })
    })

    expect(mockSignUp).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
      options: {
        data: {
          full_name: 'Test User',
          role: 'student'
        }
      }
    })
  })

  test('handles user sign in', async () => {
    const mockSignIn = jest.fn().mockResolvedValue({
      data: {
        user: {
          id: '1',
          email: '<EMAIL>'
        }
      },
      error: null
    })

    ;(mockSupabase.auth.signInWithPassword as jest.Mock) = mockSignIn

    const { result } = renderHook(() => useAuth(), { wrapper })

    await act(async () => {
      await result.current.signIn('<EMAIL>', 'password123')
    })

    expect(mockSignIn).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })

  test('handles user sign out', async () => {
    const mockSignOut = jest.fn().mockResolvedValue({
      error: null
    })

    ;(mockSupabase.auth.signOut as jest.Mock) = mockSignOut

    const { result } = renderHook(() => useAuth(), { wrapper })

    await act(async () => {
      await result.current.signOut()
    })

    expect(mockSignOut).toHaveBeenCalled()
  })

  test('handles authentication errors', async () => {
    const mockError = new Error('Invalid credentials')
    const mockSignIn = jest.fn().mockResolvedValue({
      data: null,
      error: mockError
    })

    ;(mockSupabase.auth.signInWithPassword as jest.Mock) = mockSignIn

    const { result } = renderHook(() => useAuth(), { wrapper })

    await act(async () => {
      try {
        await result.current.signIn('<EMAIL>', 'wrongpassword')
      } catch (error) {
        expect(error).toBe(mockError)
      }
    })
  })

  test('loads user profile after authentication', async () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>'
    }

    const mockProfile = {
      id: '1',
      full_name: 'Test User',
      role: 'student',
      phone: '+971501234567'
    }

    const mockSelect = jest.fn().mockReturnValue({
      eq: jest.fn().mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: mockProfile,
          error: null
        })
      })
    })

    ;(mockSupabase.from as jest.Mock).mockReturnValue({
      select: mockSelect
    })

    const { result } = renderHook(() => useAuth(), { wrapper })

    // Simulate user authentication
    await act(async () => {
      // This would normally be triggered by Supabase auth state change
      result.current.user = mockUser
    })

    expect(mockSelect).toHaveBeenCalledWith('*')
  })

  test('updates user profile', async () => {
    const mockUpdate = jest.fn().mockReturnValue({
      eq: jest.fn().mockResolvedValue({
        data: null,
        error: null
      })
    })

    ;(mockSupabase.from as jest.Mock).mockReturnValue({
      update: mockUpdate
    })

    const { result } = renderHook(() => useAuth(), { wrapper })

    const profileData = {
      full_name: 'Updated Name',
      phone: '+971501234567'
    }

    await act(async () => {
      await result.current.updateProfile(profileData)
    })

    expect(mockUpdate).toHaveBeenCalledWith(profileData)
  })

  test('handles role-based access', () => {
    const { result } = renderHook(() => useAuth(), { wrapper })

    // Test hasRole function
    act(() => {
      result.current.profile = {
        id: '1',
        role: 'student',
        full_name: 'Test User'
      }
    })

    expect(result.current.hasRole('student')).toBe(true)
    expect(result.current.hasRole('admin')).toBe(false)
    expect(result.current.hasRole(['student', 'school'])).toBe(true)
  })

  test('handles loading states correctly', () => {
    const { result } = renderHook(() => useAuth(), { wrapper })

    // Initially loading
    expect(result.current.loading).toBe(true)

    // After auth state is determined
    act(() => {
      result.current.loading = false
    })

    expect(result.current.loading).toBe(false)
  })
})

// Integration tests
describe('AuthContext Integration', () => {
  test('works with multiple components', () => {
    const TestComponent1 = () => {
      const { user } = useAuth()
      return <div>{user ? 'Authenticated' : 'Not authenticated'}</div>
    }

    const TestComponent2 = () => {
      const { profile } = useAuth()
      return <div>{profile?.full_name || 'No profile'}</div>
    }

    const { result: result1 } = renderHook(() => useAuth(), { wrapper })
    const { result: result2 } = renderHook(() => useAuth(), { wrapper })

    // Both hooks should return the same state
    expect(result1.current.user).toBe(result2.current.user)
    expect(result1.current.profile).toBe(result2.current.profile)
  })

  test('persists state across re-renders', () => {
    const { result, rerender } = renderHook(() => useAuth(), { wrapper })

    const initialUser = result.current.user
    const initialProfile = result.current.profile

    rerender()

    expect(result.current.user).toBe(initialUser)
    expect(result.current.profile).toBe(initialProfile)
  })
})

// Error handling tests
describe('AuthContext Error Handling', () => {
  test('handles network errors gracefully', async () => {
    const networkError = new Error('Network error')
    const mockSignIn = jest.fn().mockRejectedValue(networkError)

    ;(mockSupabase.auth.signInWithPassword as jest.Mock) = mockSignIn

    const { result } = renderHook(() => useAuth(), { wrapper })

    await act(async () => {
      try {
        await result.current.signIn('<EMAIL>', 'password123')
      } catch (error) {
        expect(error).toBe(networkError)
      }
    })
  })

  test('handles profile loading errors', async () => {
    const profileError = new Error('Profile not found')
    const mockSelect = jest.fn().mockReturnValue({
      eq: jest.fn().mockReturnValue({
        single: jest.fn().mockResolvedValue({
          data: null,
          error: profileError
        })
      })
    })

    ;(mockSupabase.from as jest.Mock).mockReturnValue({
      select: mockSelect
    })

    const { result } = renderHook(() => useAuth(), { wrapper })

    // Should handle profile loading error gracefully
    expect(result.current.profile).toBeNull()
  })
})

// Performance tests
describe('AuthContext Performance', () => {
  test('does not cause unnecessary re-renders', () => {
    let renderCount = 0
    
    const TestComponent = () => {
      renderCount++
      const { user } = useAuth()
      return <div>{user?.email}</div>
    }

    const { rerender } = renderHook(() => useAuth(), { wrapper })

    const initialRenderCount = renderCount

    // Multiple re-renders should not increase render count significantly
    rerender()
    rerender()
    rerender()

    expect(renderCount - initialRenderCount).toBeLessThan(5)
  })

  test('memoizes expensive operations', () => {
    const { result } = renderHook(() => useAuth(), { wrapper })

    const hasRoleFunction1 = result.current.hasRole
    const hasRoleFunction2 = result.current.hasRole

    // Function should be memoized
    expect(hasRoleFunction1).toBe(hasRoleFunction2)
  })
})
