import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

// GET - جلب عنصر قائمة واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    const { data: menuItem, error } = await supabase
      .from('menu_items')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'عنصر القائمة غير موجود' },
          { status: 404 }
        )
      }
      console.error('Error fetching menu item:', error)
      return NextResponse.json(
        { error: 'فشل في جلب عنصر القائمة' },
        { status: 500 }
      )
    }

    return NextResponse.json({ menuItem })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث عنصر قائمة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      title_ar,
      title_en,
      title_fr,
      slug,
      icon,
      parent_id,
      order_index,
      is_active,
      target_type,
      target_value
    } = body

    // التحقق من البيانات المطلوبة
    if (!title_ar || !slug || !target_type || !target_value) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار الـ slug (باستثناء العنصر الحالي)
    const { data: existingItem } = await supabase
      .from('menu_items')
      .select('id')
      .eq('slug', slug)
      .neq('id', params.id)
      .single()

    if (existingItem) {
      return NextResponse.json(
        { error: 'الرابط المختصر موجود بالفعل' },
        { status: 400 }
      )
    }

    // تحديث عنصر القائمة
    const { data: menuItem, error: updateError } = await supabase
      .from('menu_items')
      .update({
        title_ar,
        title_en: title_en || null,
        title_fr: title_fr || null,
        slug,
        icon: icon || null,
        parent_id: parent_id || null,
        order_index: order_index || 0,
        is_active: is_active ?? true,
        target_type,
        target_value
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating menu item:', updateError)
      return NextResponse.json(
        { error: 'فشل في تحديث عنصر القائمة' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم تحديث عنصر القائمة بنجاح',
      menuItem 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف عنصر قائمة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    // التحقق من وجود عناصر فرعية
    const { data: childItems } = await supabase
      .from('menu_items')
      .select('id')
      .eq('parent_id', params.id)

    if (childItems && childItems.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف عنصر يحتوي على عناصر فرعية' },
        { status: 400 }
      )
    }

    // حذف عنصر القائمة
    const { error: deleteError } = await supabase
      .from('menu_items')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting menu item:', deleteError)
      return NextResponse.json(
        { error: 'فشل في حذف عنصر القائمة' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم حذف عنصر القائمة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
