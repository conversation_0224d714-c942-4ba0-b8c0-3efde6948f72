{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport function createClient(cookieStore: ReturnType<typeof cookies>) {\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          try {\n            cookieStore.set({ name, value, ...options })\n          } catch (error) {\n            // The `set` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n        remove(name: string, options: any) {\n          try {\n            cookieStore.set({ name, value: '', ...options })\n          } catch (error) {\n            // The `delete` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS,aAAa,WAAuC;IAClE,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM;wBAAO,GAAG,OAAO;oBAAC;gBAC5C,EAAE,OAAO,OAAO;gBACd,uDAAuD;gBACvD,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM,OAAO;wBAAI,GAAG,OAAO;oBAAC;gBAChD,EAAE,OAAO,OAAO;gBACd,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/menu-items/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { cookies } from 'next/headers'\nimport { createClient } from '@/lib/supabase/server'\n\n// GET - جلب جميع عناصر القائمة\nexport async function GET(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    const { searchParams } = new URL(request.url)\n    const includeInactive = searchParams.get('include_inactive') === 'true'\n    const parentId = searchParams.get('parent_id')\n\n    let query = supabase\n      .from('menu_items')\n      .select('*')\n      .order('order_index', { ascending: true })\n\n    // تطبيق الفلاتر\n    if (!includeInactive) {\n      query = query.eq('is_active', true)\n    }\n\n    if (parentId) {\n      if (parentId === 'null') {\n        query = query.is('parent_id', null)\n      } else {\n        query = query.eq('parent_id', parentId)\n      }\n    }\n\n    const { data: menuItems, error } = await query\n\n    if (error) {\n      console.error('Error fetching menu items:', error)\n      // إرجاع قائمة فارغة بدلاً من خطأ لتجنب كسر الواجهة\n      return NextResponse.json({\n        menuItems: [],\n        warning: 'فشل في جلب عناصر القائمة من قاعدة البيانات'\n      })\n    }\n\n    return NextResponse.json({ menuItems: menuItems || [] })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    // إرجاع قائمة فارغة بدلاً من خطأ لتجنب كسر الواجهة\n    return NextResponse.json({\n      menuItems: [],\n      warning: 'خطأ غير متوقع في جلب عناصر القائمة'\n    })\n  }\n}\n\n// POST - إضافة عنصر قائمة جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات الأدمن\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بهذا الإجراء' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    const {\n      title_ar,\n      title_en,\n      title_fr,\n      slug,\n      icon,\n      parent_id,\n      order_index,\n      is_active,\n      target_type,\n      target_value\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!title_ar || !slug || !target_type || !target_value) {\n      return NextResponse.json(\n        { error: 'البيانات المطلوبة مفقودة' },\n        { status: 400 }\n      )\n    }\n\n    // التحقق من عدم تكرار الـ slug\n    const { data: existingItem } = await supabase\n      .from('menu_items')\n      .select('id')\n      .eq('slug', slug)\n      .single()\n\n    if (existingItem) {\n      return NextResponse.json(\n        { error: 'الرابط المختصر موجود بالفعل' },\n        { status: 400 }\n      )\n    }\n\n    // إدراج عنصر القائمة في قاعدة البيانات\n    const { data: menuItem, error: insertError } = await supabase\n      .from('menu_items')\n      .insert({\n        title_ar,\n        title_en: title_en || null,\n        title_fr: title_fr || null,\n        slug,\n        icon: icon || null,\n        parent_id: parent_id || null,\n        order_index: order_index || 0,\n        is_active: is_active ?? true,\n        target_type,\n        target_value\n      })\n      .select()\n      .single()\n\n    if (insertError) {\n      console.error('Error inserting menu item:', insertError)\n      return NextResponse.json(\n        { error: 'فشل في إضافة عنصر القائمة' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ \n      message: 'تم إضافة عنصر القائمة بنجاح',\n      menuItem \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - تحديث ترتيب عناصر القائمة\nexport async function PUT(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات الأدمن\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بهذا الإجراء' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    const { items } = body\n\n    if (!Array.isArray(items)) {\n      return NextResponse.json(\n        { error: 'البيانات غير صحيحة' },\n        { status: 400 }\n      )\n    }\n\n    // تحديث ترتيب العناصر\n    const updates = items.map((item, index) => \n      supabase\n        .from('menu_items')\n        .update({ order_index: index + 1 })\n        .eq('id', item.id)\n    )\n\n    await Promise.all(updates)\n\n    return NextResponse.json({ \n      message: 'تم تحديث ترتيب القائمة بنجاح'\n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,kBAAkB,aAAa,GAAG,CAAC,wBAAwB;QACjE,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,IAAI,QAAQ,SACT,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC,eAAe;YAAE,WAAW;QAAK;QAE1C,gBAAgB;QAChB,IAAI,CAAC,iBAAiB;YACpB,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,IAAI,UAAU;YACZ,IAAI,aAAa,QAAQ;gBACvB,QAAQ,MAAM,EAAE,CAAC,aAAa;YAChC,OAAO;gBACL,QAAQ,MAAM,EAAE,CAAC,aAAa;YAChC;QACF;QAEA,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM;QAEzC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,mDAAmD;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,WAAW,EAAE;gBACb,SAAS;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,WAAW,aAAa,EAAE;QAAC;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,mDAAmD;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW,EAAE;YACb,SAAS;QACX;IACF;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,2BAA2B;QAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,WAAW,EACX,SAAS,EACT,WAAW,EACX,YAAY,EACb,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,cACL,MAAM,CAAC,MACP,EAAE,CAAC,QAAQ,MACX,MAAM;QAET,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,cACL,MAAM,CAAC;YACN;YACA,UAAU,YAAY;YACtB,UAAU,YAAY;YACtB;YACA,MAAM,QAAQ;YACd,WAAW,aAAa;YACxB,aAAa,eAAe;YAC5B,WAAW,aAAa;YACxB;YACA;QACF,GACC,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,2BAA2B;QAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,MAAM,QAC/B,SACG,IAAI,CAAC,cACL,MAAM,CAAC;gBAAE,aAAa,QAAQ;YAAE,GAChC,EAAE,CAAC,MAAM,KAAK,EAAE;QAGrB,MAAM,QAAQ,GAAG,CAAC;QAElB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}