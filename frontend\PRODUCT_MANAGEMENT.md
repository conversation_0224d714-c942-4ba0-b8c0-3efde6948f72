# نظام إدارة المنتجات - Graduation Toqs

## نظرة عامة

تم إنشاء نظام شامل لإدارة المنتجات في منصة Graduation Toqs يتيح للمديرين إضافة وتعديل وحذف المنتجات بسهولة.

## المميزات الرئيسية

### 1. صفحة إدارة المنتجات (`/dashboard/admin/products`)
- عرض جميع المنتجات في جدول منظم
- إحصائيات سريعة (إجمالي المنتجات، المتاحة، المخزون المنخفض، متوسط التقييم)
- فلترة المنتجات حسب:
  - الفئة (ثوب، قبعة، وشاح، شرابة، قلنسوة)
  - حالة التوفر (متاح/غير متاح)
  - الترتيب (تاريخ الإنشاء، الاسم، السعر، المخزون، التقييم)
- البحث في أسماء ووصف المنتجات
- إجراءات سريعة (عرض، تعديل، تغيير التوفر، حذف)

### 2. نموذج إضافة المنتج
نموذج شامل مقسم إلى 4 تبويبات:

#### أ. المعلومات الأساسية
- اسم المنتج (مطلوب)
- فئة المنتج (مطلوب)
- وصف المنتج (مطلوب)
- السعر (مطلوب)
- سعر الإيجار (اختياري)
- كمية المخزون (مطلوب)
- حالة التوفر (متاح/غير متاح)

#### ب. التفاصيل والألوان
- إضافة الألوان المتاحة (من قائمة محددة مسبقاً أو مخصص)
- إضافة المقاسات المتاحة (من قائمة محددة مسبقاً أو مخصص)
- إدارة الألوان والمقاسات (إضافة/حذف)

#### ج. الصور
- رفع متعدد للصور (حتى 8 صور)
- السحب والإفلات
- معاينة فورية للصور
- ترتيب الصور (الأولى تصبح الصورة الرئيسية)
- تحديد الصورة الرئيسية
- حذف الصور الفردية
- دعم صيغ JPG, PNG, WebP
- حد أقصى 5 ميجابايت لكل صورة

#### د. المميزات والمواصفات
- إضافة مميزات المنتج (قائمة نقاط)
- إضافة مواصفات تقنية (مفتاح-قيمة)
- إدارة المميزات والمواصفات

### 3. نظام رفع الصور المتقدم
- مكون `ImageUploader` قابل لإعادة الاستخدام
- دعم السحب والإفلات
- معاينة فورية
- شريط تقدم الرفع
- التحقق من نوع وحجم الملفات
- ترتيب الصور
- حذف الصور
- مؤشرات بصرية للحالة

### 4. API Endpoints

#### المنتجات (`/api/products`)
- `GET` - جلب جميع المنتجات مع فلترة
- `POST` - إضافة منتج جديد
- `PUT` - تحديث منتج موجود
- `DELETE` - حذف منتج

#### منتج واحد (`/api/products/[id]`)
- `GET` - جلب منتج واحد
- `PUT` - تحديث منتج واحد
- `DELETE` - حذف منتج واحد

#### رفع الصور (`/api/upload`)
- `POST` - رفع صور متعددة
- `DELETE` - حذف صورة

## هيكل البيانات

### Product Interface
```typescript
interface Product {
  id: string
  name: string
  description: string
  category: 'gown' | 'cap' | 'tassel' | 'stole' | 'hood'
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
  rating?: number
  reviews_count?: number
}
```

## الأمان والصلاحيات

- جميع عمليات إدارة المنتجات محصورة بالمديرين فقط
- التحقق من صلاحيات المستخدم في كل API call
- التحقق من صحة البيانات قبل الحفظ
- حماية من رفع ملفات غير آمنة

## كيفية الاستخدام

### 1. الوصول لصفحة إدارة المنتجات
```
/dashboard/admin/products
```

### 2. إضافة منتج جديد
1. اضغط على زر "إضافة منتج جديد"
2. املأ المعلومات الأساسية
3. أضف الألوان والمقاسات
4. ارفع الصور
5. أضف المميزات والمواصفات
6. اضغط "إضافة المنتج"

### 3. تعديل منتج موجود
1. اضغط على زر التعديل بجانب المنتج
2. عدل البيانات المطلوبة
3. احفظ التغييرات

### 4. حذف منتج
1. اضغط على زر الحذف
2. أكد الحذف

## الملفات المهمة

```
frontend/src/
├── app/dashboard/admin/products/page.tsx     # صفحة إدارة المنتجات
├── components/admin/
│   ├── ProductForm.tsx                       # نموذج المنتج
│   └── ImageUploader.tsx                     # مكون رفع الصور
├── app/api/
│   ├── products/route.ts                     # API المنتجات
│   ├── products/[id]/route.ts               # API منتج واحد
│   └── upload/route.ts                       # API رفع الصور
└── supabase/schema.sql                       # هيكل قاعدة البيانات
```

## قاعدة البيانات

### جدول products
```sql
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    rental_price DECIMAL(10,2),
    colors TEXT[] DEFAULT '{}',
    sizes TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    stock_quantity INTEGER DEFAULT 0,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## التطوير المستقبلي

- [ ] إضافة تحرير مجمع للمنتجات
- [ ] تصدير المنتجات إلى Excel/CSV
- [ ] إحصائيات مفصلة للمنتجات
- [ ] نظام إشعارات للمخزون المنخفض
- [ ] تحسين محرك البحث
- [ ] إضافة فئات فرعية
- [ ] نظام تقييم المنتجات
- [ ] ربط المنتجات بالطلبات

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
