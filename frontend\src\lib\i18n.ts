export type Locale = 'ar' | 'fr' | 'en';

export const locales: Locale[] = ['ar', 'fr', 'en'];

export const defaultLocale: Locale = 'ar';

export const localeNames = {
  ar: 'العربية',
  fr: 'Français', 
  en: 'English'
};

export const localeFlags = {
  ar: '🇲🇦',
  fr: '🇫🇷',
  en: '🇬🇧'
};

export const rtlLocales: Locale[] = ['ar'];

export function isRtlLocale(locale: Locale): boolean {
  return rtlLocales.includes(locale);
}
