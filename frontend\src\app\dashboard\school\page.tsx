"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  School,
  Users,
  ShoppingCart,
  TrendingUp,
  Calendar,
  FileText,
  Download,
  Upload,
  Plus,
  Eye,
  Edit,
  Search,
  Filter,
  BarChart3,
  PieChart,
  DollarSign,
  Package
} from 'lucide-react'

// أنواع البيانات
interface Student {
  id: string
  name: string
  email: string
  class: string
  orders_count: number
  total_spent: number
  status: 'active' | 'graduated' | 'inactive'
}

interface SchoolOrder {
  id: string
  student_name: string
  student_class: string
  items: string[]
  total: number
  status: 'pending' | 'confirmed' | 'completed'
  created_at: string
}

interface SchoolStats {
  total_students: number
  active_orders: number
  total_revenue: number
  completion_rate: number
}

export default function SchoolDashboard() {
  const { user, profile } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [students, setStudents] = useState<Student[]>([])
  const [orders, setOrders] = useState<SchoolOrder[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total_students: 0,
    active_orders: 0,
    total_revenue: 0,
    completion_rate: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockStudents: Student[] = [
      {
        id: '1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        class: 'الصف الثاني عشر - أ',
        orders_count: 2,
        total_spent: 389.98,
        status: 'active'
      },
      {
        id: '2',
        name: 'فاطمة أحمد حسن',
        email: '<EMAIL>',
        class: 'الصف الثاني عشر - ب',
        orders_count: 1,
        total_spent: 299.99,
        status: 'active'
      },
      {
        id: '3',
        name: 'محمد عبدالله سالم',
        email: '<EMAIL>',
        class: 'الصف الثاني عشر - أ',
        orders_count: 3,
        total_spent: 567.97,
        status: 'graduated'
      }
    ]

    const mockOrders: SchoolOrder[] = [
      {
        id: '1',
        student_name: 'أحمد محمد علي',
        student_class: 'الصف الثاني عشر - أ',
        items: ['زي التخرج الكلاسيكي', 'قبعة التخرج'],
        total: 389.98,
        status: 'pending',
        created_at: '2024-01-20'
      },
      {
        id: '2',
        student_name: 'فاطمة أحمد حسن',
        student_class: 'الصف الثاني عشر - ب',
        items: ['زي التخرج المميز'],
        total: 299.99,
        status: 'confirmed',
        created_at: '2024-01-19'
      }
    ]

    const mockStats: SchoolStats = {
      total_students: mockStudents.length,
      active_orders: mockOrders.filter(o => o.status !== 'completed').length,
      total_revenue: mockOrders.reduce((sum, order) => sum + order.total, 0),
      completion_rate: 85
    }

    setStudents(mockStudents)
    setOrders(mockOrders)
    setStats(mockStats)
    setLoading(false)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'graduated': return 'bg-blue-100 text-blue-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط'
      case 'graduated': return 'متخرج'
      case 'inactive': return 'غير نشط'
      case 'pending': return 'في الانتظار'
      case 'confirmed': return 'مؤكد'
      case 'completed': return 'مكتمل'
      default: return 'غير معروف'
    }
  }

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.class.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                لوحة تحكم المدرسة 🏫
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة طلاب المدرسة وطلبات أزياء التخرج
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                تصدير البيانات
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                إضافة طالب
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي الطلاب
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_students}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    الطلبات النشطة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.active_orders}
                  </p>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي الإيرادات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_revenue.toFixed(2)} درهم
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    معدل الإنجاز
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.completion_rate}%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="arabic-text">
              <BarChart3 className="h-4 w-4 mr-2" />
              نظرة عامة
            </TabsTrigger>
            <TabsTrigger value="students" className="arabic-text">
              <Users className="h-4 w-4 mr-2" />
              الطلاب
            </TabsTrigger>
            <TabsTrigger value="orders" className="arabic-text">
              <Package className="h-4 w-4 mr-2" />
              الطلبات
            </TabsTrigger>
            <TabsTrigger value="reports" className="arabic-text">
              <FileText className="h-4 w-4 mr-2" />
              التقارير
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Charts Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إحصائيات الطلبات الشهرية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للطلبات</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">توزيع الطلبات حسب الصف</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <PieChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم دائري للتوزيع</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">النشاط الأخير</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orders.slice(0, 5).map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                          <ShoppingCart className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium arabic-text">{order.student_name}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {order.student_class} - {order.items.join(', ')}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{order.total} درهم</p>
                        <Badge className={getStatusColor(order.status)}>
                          {getStatusText(order.status)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Students Tab */}
          <TabsContent value="students" className="space-y-6 mt-6">
            {/* Search and Filters */}
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="البحث عن طالب..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 arabic-text"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                فلترة
              </Button>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                استيراد
              </Button>
            </div>

            {/* Students List */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">قائمة الطلاب</CardTitle>
                <CardDescription className="arabic-text">
                  إدارة طلاب المدرسة وطلباتهم
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredStudents.map((student) => (
                    <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                          <Users className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <p className="font-medium arabic-text">{student.name}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {student.class}
                          </p>
                          <p className="text-sm text-gray-500">
                            {student.email}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(student.status)}>
                          {getStatusText(student.status)}
                        </Badge>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text">
                          {student.orders_count} طلب - {student.total_spent} درهم
                        </p>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-6 mt-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold arabic-text">طلبات المدرسة</h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                طلب جماعي جديد
              </Button>
            </div>

            <div className="grid gap-6">
              {orders.map((order) => (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="arabic-text">طلب #{order.id}</CardTitle>
                        <CardDescription className="arabic-text">
                          {order.student_name} - {order.student_class}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(order.status)}>
                        {getStatusText(order.status)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium arabic-text mb-2">العناصر المطلوبة:</p>
                        <div className="flex flex-wrap gap-2">
                          {order.items.map((item, index) => (
                            <Badge key={index} variant="outline" className="arabic-text">
                              {item}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex justify-between items-center pt-3 border-t">
                        <span className="font-medium arabic-text">الإجمالي:</span>
                        <span className="text-lg font-bold text-green-600">{order.total} درهم</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          عرض التفاصيل
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          تعديل الطلب
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          تحميل الفاتورة
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تقرير الطلبات الشهري</CardTitle>
                  <CardDescription className="arabic-text">
                    إحصائيات مفصلة عن طلبات الشهر الحالي
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="arabic-text">إجمالي الطلبات:</span>
                      <span className="font-bold">{orders.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">الطلبات المكتملة:</span>
                      <span className="font-bold text-green-600">
                        {orders.filter(o => o.status === 'completed').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">الطلبات المعلقة:</span>
                      <span className="font-bold text-yellow-600">
                        {orders.filter(o => o.status === 'pending').length}
                      </span>
                    </div>
                    <div className="pt-4">
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        تحميل التقرير
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تقرير الطلاب</CardTitle>
                  <CardDescription className="arabic-text">
                    إحصائيات عن نشاط الطلاب
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="arabic-text">الطلاب النشطون:</span>
                      <span className="font-bold text-green-600">
                        {students.filter(s => s.status === 'active').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">المتخرجون:</span>
                      <span className="font-bold text-blue-600">
                        {students.filter(s => s.status === 'graduated').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">متوسط الإنفاق:</span>
                      <span className="font-bold">
                        {(students.reduce((sum, s) => sum + s.total_spent, 0) / students.length).toFixed(2)} درهم
                      </span>
                    </div>
                    <div className="pt-4">
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        تحميل التقرير
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-6 mt-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold arabic-text">طلبات المدرسة</h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                طلب جماعي جديد
              </Button>
            </div>

            <div className="grid gap-6">
              {orders.map((order) => (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="arabic-text">طلب #{order.id}</CardTitle>
                        <CardDescription className="arabic-text">
                          {order.student_name} - {order.student_class}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(order.status)}>
                        {getStatusText(order.status)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium arabic-text mb-2">العناصر المطلوبة:</p>
                        <div className="flex flex-wrap gap-2">
                          {order.items.map((item, index) => (
                            <Badge key={index} variant="outline" className="arabic-text">
                              {item}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex justify-between items-center pt-3 border-t">
                        <span className="font-medium arabic-text">الإجمالي:</span>
                        <span className="text-lg font-bold text-green-600">{order.total} درهم</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          عرض التفاصيل
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          تعديل الطلب
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          تحميل الفاتورة
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تقرير الطلبات الشهري</CardTitle>
                  <CardDescription className="arabic-text">
                    إحصائيات مفصلة عن طلبات الشهر الحالي
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="arabic-text">إجمالي الطلبات:</span>
                      <span className="font-bold">{orders.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">الطلبات المكتملة:</span>
                      <span className="font-bold text-green-600">
                        {orders.filter(o => o.status === 'completed').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">الطلبات المعلقة:</span>
                      <span className="font-bold text-yellow-600">
                        {orders.filter(o => o.status === 'pending').length}
                      </span>
                    </div>
                    <div className="pt-4">
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        تحميل التقرير
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تقرير الطلاب</CardTitle>
                  <CardDescription className="arabic-text">
                    إحصائيات عن نشاط الطلاب
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="arabic-text">الطلاب النشطون:</span>
                      <span className="font-bold text-green-600">
                        {students.filter(s => s.status === 'active').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">المتخرجون:</span>
                      <span className="font-bold text-blue-600">
                        {students.filter(s => s.status === 'graduated').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">متوسط الإنفاق:</span>
                      <span className="font-bold">
                        {(students.reduce((sum, s) => sum + s.total_spent, 0) / students.length).toFixed(2)} درهم
                      </span>
                    </div>
                    <div className="pt-4">
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        تحميل التقرير
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
