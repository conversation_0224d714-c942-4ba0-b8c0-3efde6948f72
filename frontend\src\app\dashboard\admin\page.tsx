"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { 
  Shield,
  Users,
  ShoppingCart,
  TrendingUp,
  Calendar,
  FileText,
  Download,
  Upload,
  Plus,
  Eye,
  Edit,
  Search,
  Filter,
  BarChart3,
  PieChart,
  DollarSign,
  Package,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  School,
  Truck
} from 'lucide-react'

// أنواع البيانات
interface AdminStats {
  total_users: number
  total_schools: number
  total_orders: number
  total_revenue: number
  monthly_growth: number
  active_deliveries: number
}

interface SystemAlert {
  id: string
  type: 'error' | 'warning' | 'info' | 'success'
  title: string
  message: string
  timestamp: string
  resolved: boolean
}

interface RecentActivity {
  id: string
  type: 'order' | 'user' | 'school' | 'system'
  description: string
  timestamp: string
  user?: string
}

export default function AdminDashboard() {
  const { user, profile } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<AdminStats>({
    total_users: 0,
    total_schools: 0,
    total_orders: 0,
    total_revenue: 0,
    monthly_growth: 0,
    active_deliveries: 0
  })
  const [alerts, setAlerts] = useState<SystemAlert[]>([])
  const [activities, setActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockStats: AdminStats = {
      total_users: 1247,
      total_schools: 45,
      total_orders: 3892,
      total_revenue: 1234567.89,
      monthly_growth: 12.5,
      active_deliveries: 156
    }

    const mockAlerts: SystemAlert[] = [
      {
        id: '1',
        type: 'warning',
        title: 'مخزون منخفض',
        message: 'مخزون أزياء التخرج الكلاسيكية أقل من 50 قطعة',
        timestamp: '2024-01-20T10:30:00Z',
        resolved: false
      },
      {
        id: '2',
        type: 'info',
        title: 'طلب جماعي جديد',
        message: 'مدرسة الأمل قدمت طلب جماعي لـ 120 طالب',
        timestamp: '2024-01-20T09:15:00Z',
        resolved: false
      },
      {
        id: '3',
        type: 'success',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',
        timestamp: '2024-01-19T22:00:00Z',
        resolved: true
      }
    ]

    const mockActivities: RecentActivity[] = [
      {
        id: '1',
        type: 'order',
        description: 'طلب جديد من أحمد محمد - زي التخرج الكلاسيكي',
        timestamp: '2024-01-20T11:00:00Z',
        user: 'أحمد محمد'
      },
      {
        id: '2',
        type: 'school',
        description: 'تسجيل مدرسة جديدة - مدرسة النور الثانوية',
        timestamp: '2024-01-20T10:45:00Z'
      },
      {
        id: '3',
        type: 'user',
        description: 'مستخدم جديد انضم للمنصة - فاطمة أحمد',
        timestamp: '2024-01-20T10:30:00Z',
        user: 'فاطمة أحمد'
      },
      {
        id: '4',
        type: 'system',
        description: 'تم إنشاء نسخة احتياطية من قاعدة البيانات',
        timestamp: '2024-01-20T02:00:00Z'
      }
    ]

    setStats(mockStats)
    setAlerts(mockAlerts)
    setActivities(mockActivities)
    setLoading(false)
  }, [])

  const getAlertIcon = (type: SystemAlert['type']) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'info': return <Clock className="h-5 w-5 text-blue-600" />
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />
      default: return <Clock className="h-5 w-5 text-gray-600" />
    }
  }

  const getAlertColor = (type: SystemAlert['type']) => {
    switch (type) {
      case 'error': return 'bg-red-50 border-red-200 dark:bg-red-900/20'
      case 'warning': return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20'
      case 'info': return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20'
      case 'success': return 'bg-green-50 border-green-200 dark:bg-green-900/20'
      default: return 'bg-gray-50 border-gray-200 dark:bg-gray-900/20'
    }
  }

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'order': return <ShoppingCart className="h-4 w-4 text-blue-600" />
      case 'user': return <Users className="h-4 w-4 text-green-600" />
      case 'school': return <School className="h-4 w-4 text-purple-600" />
      case 'system': return <Settings className="h-4 w-4 text-gray-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                لوحة تحكم الإدارة 👑
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة شاملة للمنصة والمستخدمين والطلبات
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                تصدير التقارير
              </Button>
              <Button size="sm">
                <Settings className="h-4 w-4 mr-2" />
                إعدادات النظام
              </Button>
            </div>
          </div>
        </div>

        {/* System Alerts */}
        {alerts.filter(a => !a.resolved).length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4 arabic-text">تنبيهات النظام</h2>
            <div className="grid gap-4">
              {alerts.filter(a => !a.resolved).slice(0, 3).map((alert) => (
                <div key={alert.id} className={`p-4 rounded-lg border ${getAlertColor(alert.type)}`}>
                  <div className="flex items-start gap-3">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <h3 className="font-medium arabic-text">{alert.title}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text">
                        {alert.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {new Date(alert.timestamp).toLocaleString('ar-SA')}
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      حل
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي المستخدمين
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_users.toLocaleString()}
                  </p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    المدارس المسجلة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_schools}
                  </p>
                </div>
                <School className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي الطلبات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_orders.toLocaleString()}
                  </p>
                </div>
                <ShoppingCart className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي الإيرادات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {(stats.total_revenue / 1000000).toFixed(1)}M درهم
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    النمو الشهري
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    +{stats.monthly_growth}%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    عمليات التوصيل
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.active_deliveries}
                  </p>
                </div>
                <Truck className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview" className="arabic-text">
              <BarChart3 className="h-4 w-4 mr-2" />
              نظرة عامة
            </TabsTrigger>
            <TabsTrigger value="users" className="arabic-text">
              <Users className="h-4 w-4 mr-2" />
              المستخدمين
            </TabsTrigger>
            <TabsTrigger value="orders" className="arabic-text">
              <Package className="h-4 w-4 mr-2" />
              الطلبات
            </TabsTrigger>
            <TabsTrigger value="analytics" className="arabic-text">
              <PieChart className="h-4 w-4 mr-2" />
              التحليلات
            </TabsTrigger>
            <TabsTrigger value="settings" className="arabic-text">
              <Settings className="h-4 w-4 mr-2" />
              الإعدادات
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الإيرادات الشهرية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للإيرادات</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* User Growth Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">نمو المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للنمو</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">النشاط الأخير</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activities.slice(0, 8).map((activity) => (
                    <div key={activity.id} className="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="w-8 h-8 bg-white dark:bg-gray-700 rounded-full flex items-center justify-center">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm arabic-text">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(activity.timestamp).toLocaleString('ar-SA')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
