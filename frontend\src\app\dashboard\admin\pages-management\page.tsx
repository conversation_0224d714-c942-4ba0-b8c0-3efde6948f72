"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  FileText,
  Calendar,
  User,
  ArrowLeft,
  ExternalLink
} from 'lucide-react'
import { toast } from 'sonner'

// أنواع البيانات
interface Page {
  id: string
  slug: string
  is_published: boolean
  author_id: string
  featured_image?: string
  created_at: string
  updated_at: string
  page_content: PageContent[]
  profiles?: {
    full_name: string
  }
}

interface PageContent {
  id: string
  page_id: string
  language: 'ar' | 'en' | 'fr'
  title: string
  content: string
  meta_description?: string
  meta_keywords?: string
}

interface PageForm {
  slug: string
  is_published: boolean
  featured_image: string
  content: {
    ar: {
      title: string
      content: string
      meta_description: string
      meta_keywords: string
    }
    en: {
      title: string
      content: string
      meta_description: string
      meta_keywords: string
    }
    fr: {
      title: string
      content: string
      meta_description: string
      meta_keywords: string
    }
  }
}

export default function PagesManagementPage() {
  const { user, profile } = useAuth()
  const [pages, setPages] = useState<Page[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingPage, setEditingPage] = useState<Page | null>(null)
  const [formData, setFormData] = useState<PageForm>({
    slug: '',
    is_published: false,
    featured_image: '',
    content: {
      ar: { title: '', content: '', meta_description: '', meta_keywords: '' },
      en: { title: '', content: '', meta_description: '', meta_keywords: '' },
      fr: { title: '', content: '', meta_description: '', meta_keywords: '' }
    }
  })

  // التحقق من صلاحيات الأدمن
  useEffect(() => {
    if (!user || !profile || profile.role !== 'admin') {
      window.location.href = '/dashboard'
      return
    }
  }, [user, profile])

  // جلب الصفحات
  const fetchPages = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/pages?include_unpublished=true&language=ar')
      const data = await response.json()
      
      if (response.ok) {
        setPages(data.pages || [])
      } else {
        toast.error(data.error || 'فشل في جلب الصفحات')
      }
    } catch (error) {
      console.error('Error fetching pages:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPages()
  }, [])

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      slug: '',
      is_published: false,
      featured_image: '',
      content: {
        ar: { title: '', content: '', meta_description: '', meta_keywords: '' },
        en: { title: '', content: '', meta_description: '', meta_keywords: '' },
        fr: { title: '', content: '', meta_description: '', meta_keywords: '' }
      }
    })
    setEditingPage(null)
  }

  // فتح نموذج التحرير
  const openEditDialog = async (page: Page) => {
    try {
      // جلب تفاصيل الصفحة مع جميع اللغات
      const response = await fetch(`/api/pages/${page.id}`)
      const data = await response.json()
      
      if (response.ok) {
        const fullPage = data.page
        setEditingPage(fullPage)
        
        // تنظيم المحتوى حسب اللغة
        const contentByLang = {
          ar: { title: '', content: '', meta_description: '', meta_keywords: '' },
          en: { title: '', content: '', meta_description: '', meta_keywords: '' },
          fr: { title: '', content: '', meta_description: '', meta_keywords: '' }
        }
        
        fullPage.page_content?.forEach((content: PageContent) => {
          contentByLang[content.language] = {
            title: content.title,
            content: content.content,
            meta_description: content.meta_description || '',
            meta_keywords: content.meta_keywords || ''
          }
        })
        
        setFormData({
          slug: fullPage.slug,
          is_published: fullPage.is_published,
          featured_image: fullPage.featured_image || '',
          content: contentByLang
        })
        setIsDialogOpen(true)
      } else {
        toast.error(data.error || 'فشل في جلب تفاصيل الصفحة')
      }
    } catch (error) {
      console.error('Error fetching page details:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // حفظ الصفحة
  const savePage = async () => {
    try {
      // التحقق من البيانات المطلوبة
      if (!formData.slug || !formData.content.ar.title || !formData.content.ar.content) {
        toast.error('يرجى ملء البيانات المطلوبة (الرابط المختصر والعنوان والمحتوى بالعربية)')
        return
      }

      const url = editingPage 
        ? `/api/pages/${editingPage.id}`
        : '/api/pages'
      
      const method = editingPage ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        setIsDialogOpen(false)
        resetForm()
        fetchPages()
      } else {
        toast.error(data.error || 'فشل في حفظ الصفحة')
      }
    } catch (error) {
      console.error('Error saving page:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // حذف الصفحة
  const deletePage = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه الصفحة؟')) {
      return
    }

    try {
      const response = await fetch(`/api/pages/${id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        fetchPages()
      } else {
        toast.error(data.error || 'فشل في حذف الصفحة')
      }
    } catch (error) {
      console.error('Error deleting page:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // تبديل حالة النشر
  const togglePageStatus = async (page: Page) => {
    try {
      const response = await fetch(`/api/pages/${page.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          slug: page.slug,
          is_published: !page.is_published,
          featured_image: page.featured_image,
          content: {
            ar: {
              title: page.page_content?.[0]?.title || '',
              content: page.page_content?.[0]?.content || '',
              meta_description: page.page_content?.[0]?.meta_description || '',
              meta_keywords: page.page_content?.[0]?.meta_keywords || ''
            }
          }
        }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        fetchPages()
      } else {
        toast.error(data.error || 'فشل في تحديث حالة الصفحة')
      }
    } catch (error) {
      console.error('Error toggling page status:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // تحديث محتوى اللغة
  const updateLanguageContent = (language: 'ar' | 'en' | 'fr', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      content: {
        ...prev.content,
        [language]: {
          ...prev.content[language],
          [field]: value
        }
      }
    }))
  }

  if (!user || !profile || profile.role !== 'admin') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <a href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </a>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة الصفحات 📄
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إنشاء وتحرير الصفحات الديناميكية للمنصة
              </p>
            </div>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة صفحة جديدة
                </Button>
              </DialogTrigger>
              
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="arabic-text">
                    {editingPage ? 'تحرير الصفحة' : 'إضافة صفحة جديدة'}
                  </DialogTitle>
                  <DialogDescription className="arabic-text">
                    املأ البيانات أدناه لإنشاء أو تحديث الصفحة
                  </DialogDescription>
                </DialogHeader>

                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic" className="arabic-text">البيانات الأساسية</TabsTrigger>
                    <TabsTrigger value="ar" className="arabic-text">العربية</TabsTrigger>
                    <TabsTrigger value="en" className="arabic-text">الإنجليزية</TabsTrigger>
                    <TabsTrigger value="fr" className="arabic-text">الفرنسية</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="slug" className="arabic-text">الرابط المختصر *</Label>
                        <Input
                          id="slug"
                          value={formData.slug}
                          onChange={(e) => setFormData({...formData, slug: e.target.value})}
                          placeholder="مثال: about-us"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="featured_image" className="arabic-text">الصورة المميزة</Label>
                        <Input
                          id="featured_image"
                          value={formData.featured_image}
                          onChange={(e) => setFormData({...formData, featured_image: e.target.value})}
                          placeholder="رابط الصورة"
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is_published"
                        checked={formData.is_published}
                        onCheckedChange={(checked) => setFormData({...formData, is_published: checked})}
                      />
                      <Label htmlFor="is_published" className="arabic-text">منشور</Label>
                    </div>
                  </TabsContent>

                  {(['ar', 'en', 'fr'] as const).map((lang) => (
                    <TabsContent key={lang} value={lang} className="space-y-4">
                      <div>
                        <Label htmlFor={`title_${lang}`} className="arabic-text">
                          العنوان {lang === 'ar' ? '(مطلوب)' : ''}
                        </Label>
                        <Input
                          id={`title_${lang}`}
                          value={formData.content[lang].title}
                          onChange={(e) => updateLanguageContent(lang, 'title', e.target.value)}
                          placeholder={`العنوان بـ${lang === 'ar' ? 'العربية' : lang === 'en' ? 'الإنجليزية' : 'الفرنسية'}`}
                          className={lang === 'ar' ? 'arabic-text' : ''}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor={`content_${lang}`} className="arabic-text">
                          المحتوى {lang === 'ar' ? '(مطلوب)' : ''}
                        </Label>
                        <Textarea
                          id={`content_${lang}`}
                          value={formData.content[lang].content}
                          onChange={(e) => updateLanguageContent(lang, 'content', e.target.value)}
                          placeholder={`المحتوى بـ${lang === 'ar' ? 'العربية' : lang === 'en' ? 'الإنجليزية' : 'الفرنسية'}`}
                          rows={10}
                          className={lang === 'ar' ? 'arabic-text' : ''}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor={`meta_description_${lang}`} className="arabic-text">وصف SEO</Label>
                        <Textarea
                          id={`meta_description_${lang}`}
                          value={formData.content[lang].meta_description}
                          onChange={(e) => updateLanguageContent(lang, 'meta_description', e.target.value)}
                          placeholder="وصف مختصر للصفحة لمحركات البحث"
                          rows={3}
                          className={lang === 'ar' ? 'arabic-text' : ''}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor={`meta_keywords_${lang}`} className="arabic-text">الكلمات المفتاحية</Label>
                        <Input
                          id={`meta_keywords_${lang}`}
                          value={formData.content[lang].meta_keywords}
                          onChange={(e) => updateLanguageContent(lang, 'meta_keywords', e.target.value)}
                          placeholder="كلمة1, كلمة2, كلمة3"
                          className={lang === 'ar' ? 'arabic-text' : ''}
                        />
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    إلغاء
                  </Button>
                  <Button onClick={savePage}>
                    {editingPage ? 'تحديث' : 'إضافة'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Pages List */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">الصفحات الحالية</CardTitle>
            <CardDescription className="arabic-text">
              إدارة الصفحات الديناميكية للمنصة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 arabic-text">جاري التحميل...</p>
              </div>
            ) : pages.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 arabic-text">لا توجد صفحات</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {pages.map((page) => (
                  <Card key={page.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg arabic-text line-clamp-2">
                            {page.page_content?.[0]?.title || 'بدون عنوان'}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={page.is_published ? 'default' : 'secondary'} className="arabic-text">
                              {page.is_published ? 'منشور' : 'مسودة'}
                            </Badge>
                            <span className="text-sm text-gray-500">/{page.slug}</span>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span className="arabic-text">{page.profiles?.full_name || 'غير محدد'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(page.created_at).toLocaleDateString('ar-SA')}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 mt-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => togglePageStatus(page)}
                        >
                          {page.is_published ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(page)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deletePage(page.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        {page.is_published && (
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                          >
                            <a href={`/pages/${page.slug}`} target="_blank">
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
