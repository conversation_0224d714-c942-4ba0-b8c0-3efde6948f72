"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTranslation } from '@/hooks/useTranslation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import { UserMenu } from '@/components/auth/UserMenu'
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown'
import { Badge } from '@/components/ui/badge'
import {
  GraduationCap,
  Home,
  ShoppingBag,
  Palette,
  Info,
  Phone,
  Search,
  Heart,
  ExternalLink,
  FileText,
  Link as LinkIcon
} from 'lucide-react'

// أنواع البيانات لعناصر القائمة
interface MenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id?: string
  order_index: number
  is_active: boolean
  target_type: 'internal' | 'external' | 'page'
  target_value: string
}

export function Navigation() {
  const { t, locale } = useTranslation()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [cartItemsCount, setCartItemsCount] = useState(0)
  const [wishlistCount, setWishlistCount] = useState(0)
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Mock cart and wishlist counts - replace with actual data
  useEffect(() => {
    // Simulate getting cart items from localStorage or API
    const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]')
    setCartItemsCount(cartItems.length)

    const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]')
    setWishlistCount(wishlistItems.length)
  }, [])

  // جلب عناصر القائمة من قاعدة البيانات
  useEffect(() => {
    const fetchMenuItems = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/menu-items?parent_id=null')

        if (response.ok) {
          const data = await response.json()
          setMenuItems(data.menuItems || [])
        } else {
          // في حالة فشل API، استخدم القائمة الافتراضية
          console.warn('Failed to fetch menu items from API, using default menu')
          setMenuItems([])
        }
      } catch (error) {
        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية
        console.warn('Error fetching menu items, using default menu:', error)
        setMenuItems([])
      } finally {
        setLoading(false)
      }
    }

    fetchMenuItems()
  }, [])

  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون
  const getNavItemsFromDB = () => {
    return menuItems.map(item => {
      // اختيار العنوان حسب اللغة الحالية
      let label = item.title_ar // افتراضي
      if (locale === 'en' && item.title_en) {
        label = item.title_en
      } else if (locale === 'fr' && item.title_fr) {
        label = item.title_fr
      }

      // تحديد الرابط حسب نوع الهدف
      let href = item.target_value
      if (item.target_type === 'page') {
        href = `/pages/${item.target_value}`
      }

      // تحديد الأيقونة
      let icon = <LinkIcon className="h-4 w-4" />
      if (item.icon) {
        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون
        switch (item.icon) {
          case 'Home':
            icon = <Home className="h-4 w-4" />
            break
          case 'ShoppingBag':
            icon = <ShoppingBag className="h-4 w-4" />
            break
          case 'Palette':
            icon = <Palette className="h-4 w-4" />
            break
          case 'Search':
            icon = <Search className="h-4 w-4" />
            break
          case 'Info':
            icon = <Info className="h-4 w-4" />
            break
          case 'Phone':
            icon = <Phone className="h-4 w-4" />
            break
          case 'ExternalLink':
            icon = <ExternalLink className="h-4 w-4" />
            break
          case 'FileText':
            icon = <FileText className="h-4 w-4" />
            break
          default:
            icon = <LinkIcon className="h-4 w-4" />
        }
      }

      return {
        href,
        label,
        icon,
        target_type: item.target_type
      }
    })
  }

  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)
  const defaultNavItems = [
    {
      href: '/',
      label: t('navigation.home'),
      icon: <Home className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/catalog',
      label: t('navigation.catalog'),
      icon: <ShoppingBag className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/customize',
      label: t('navigation.customize'),
      icon: <Palette className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/track-order',
      label: t('navigation.trackOrder') || 'تتبع الطلب',
      icon: <Search className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/about',
      label: t('navigation.about') || 'من نحن',
      icon: <Info className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/contact',
      label: t('navigation.contact') || 'تواصل معنا',
      icon: <Phone className="h-4 w-4" />,
      target_type: 'internal' as const
    }
  ]

  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية
  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)

  // إضافة رابط لوحة التحكم حسب دور المستخدم
  const { user, profile } = useAuth()
  const getDashboardLink = () => {
    if (!user || !profile) return null

    switch (profile.role) {
      case 'student':
        return { href: '/dashboard/student', label: 'لوحة التحكم', icon: <GraduationCap className="h-4 w-4" />, target_type: 'internal' as const }
      case 'school':
        return { href: '/dashboard/school', label: 'لوحة المدرسة', icon: <GraduationCap className="h-4 w-4" />, target_type: 'internal' as const }
      case 'admin':
        return { href: '/dashboard/admin', label: 'لوحة الإدارة', icon: <GraduationCap className="h-4 w-4" />, target_type: 'internal' as const }
      case 'delivery':
        return { href: '/dashboard/delivery', label: 'لوحة التوصيل', icon: <GraduationCap className="h-4 w-4" />, target_type: 'internal' as const }
      default:
        return null
    }
  }

  const dashboardLink = getDashboardLink()
  const allNavItems = dashboardLink ? [...navItems, dashboardLink] : navItems

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <header className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-3">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center gap-3 hover:opacity-80 transition-all duration-300 group"
          >
            <div className="relative">
              <GraduationCap className="h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-gray-900 dark:text-white leading-tight">
                Graduation Toqs
              </span>
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-1">
            {allNavItems.map((item) => {
              // تحديد ما إذا كان الرابط خارجي
              const isExternal = item.target_type === 'external'
              const linkProps = isExternal
                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }
                : { href: item.href }

              return (
                <Link
                  key={item.href}
                  {...linkProps}
                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${
                    isActive(item.href)
                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                  }`}
                >
                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>
                    {item.icon}
                  </span>
                  <span className="text-sm font-medium">
                    {item.label}
                  </span>
                  {isExternal && (
                    <ExternalLink className="h-3 w-3 opacity-60" />
                  )}
                  {isActive(item.href) && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"></div>
                  )}
                </Link>
              )
            })}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center gap-2">
            {/* Wishlist */}
            <Button variant="ghost" size="sm" className="relative group" asChild>
              <Link href="/wishlist">
                <Heart className="h-5 w-5 transition-colors group-hover:text-red-500" />
                {wishlistCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse"
                  >
                    {wishlistCount > 99 ? '99+' : wishlistCount}
                  </Badge>
                )}
              </Link>
            </Button>

            {/* Cart Icon */}
            <Button variant="ghost" size="sm" className="relative group" asChild>
              <Link href="/cart">
                <ShoppingBag className="h-5 w-5 transition-colors group-hover:text-blue-600" />
                {cartItemsCount > 0 && (
                  <Badge
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse"
                  >
                    {cartItemsCount > 99 ? '99+' : cartItemsCount}
                  </Badge>
                )}
              </Link>
            </Button>

            {/* Notifications */}
            <NotificationDropdown />

            {/* Divider */}
            <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

            {/* Language & Theme Controls */}
            <div className="flex items-center gap-1">
              <LanguageToggle />
              <ThemeToggle />
            </div>

            {/* User Menu */}
            <UserMenu />
          </div>

          {/* Mobile Actions */}
          <div className="flex lg:hidden items-center gap-2">
            {/* Mobile Cart */}
            <Button variant="ghost" size="sm" className="relative" asChild>
              <Link href="/cart">
                <ShoppingBag className="h-5 w-5" />
                {cartItemsCount > 0 && (
                  <Badge
                    className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600"
                  >
                    {cartItemsCount > 9 ? '9+' : cartItemsCount}
                  </Badge>
                )}
              </Link>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="relative"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <div className="relative w-6 h-6 flex items-center justify-center">
                <span
                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${
                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'
                  }`}
                />
                <span
                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${
                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'
                  }`}
                />
                <span
                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${
                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'
                  }`}
                />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${
            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="border-t border-gray-200 dark:border-gray-700 py-4">
            <nav className="flex flex-col gap-1 mb-6">
              {allNavItems.map((item, index) => {
                // تحديد ما إذا كان الرابط خارجي
                const isExternal = item.target_type === 'external'
                const linkProps = isExternal
                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }
                  : { href: item.href }

                return (
                  <Link
                    key={item.href}
                    {...linkProps}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${
                      isActive(item.href)
                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    }`}
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animation: isMobileMenuOpen ? 'slideInFromRight 0.3s ease-out forwards' : 'none'
                    }}
                  >
                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>
                      {item.icon}
                    </span>
                    <span className="text-sm">
                      {item.label}
                    </span>
                    {isExternal && (
                      <ExternalLink className="h-3 w-3 opacity-60" />
                    )}
                  </Link>
                )
              })}
            </nav>

            {/* Mobile Actions */}
            <div className="flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <LanguageToggle />
                <ThemeToggle />
              </div>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" className="relative" asChild>
                  <Link href="/wishlist">
                    <Heart className="h-5 w-5" />
                    {wishlistCount > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs"
                      >
                        {wishlistCount > 9 ? '9+' : wishlistCount}
                      </Badge>
                    )}
                  </Link>
                </Button>
                <UserMenu />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Navigation
