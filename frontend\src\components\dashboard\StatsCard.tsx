"use client"

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface StatsCardProps {
  title: string
  value: string | number
  icon: React.ReactNode
  change?: {
    value: number
    type: 'increase' | 'decrease' | 'neutral'
    period: string
  }
  progress?: {
    value: number
    max: number
    label?: string
  }
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'orange'
  className?: string
}

export function StatsCard({
  title,
  value,
  icon,
  change,
  progress,
  color = 'blue',
  className = ""
}: StatsCardProps) {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return {
          icon: 'text-blue-600',
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800'
        }
      case 'green':
        return {
          icon: 'text-green-600',
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-800'
        }
      case 'yellow':
        return {
          icon: 'text-yellow-600',
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-200 dark:border-yellow-800'
        }
      case 'red':
        return {
          icon: 'text-red-600',
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-800'
        }
      case 'purple':
        return {
          icon: 'text-purple-600',
          bg: 'bg-purple-50 dark:bg-purple-900/20',
          border: 'border-purple-200 dark:border-purple-800'
        }
      case 'orange':
        return {
          icon: 'text-orange-600',
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          border: 'border-orange-200 dark:border-orange-800'
        }
      default:
        return {
          icon: 'text-gray-600',
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-800'
        }
    }
  }

  const getChangeIcon = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return <TrendingUp className="h-3 w-3" />
      case 'decrease':
        return <TrendingDown className="h-3 w-3" />
      case 'neutral':
        return <Minus className="h-3 w-3" />
    }
  }

  const getChangeColor = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20'
      case 'decrease':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20'
      case 'neutral':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20'
    }
  }

  const colorClasses = getColorClasses(color)

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text mb-1">
              {title}
            </p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
          </div>
          <div className={`p-3 rounded-full ${colorClasses.bg} ${colorClasses.border} border`}>
            <div className={`h-6 w-6 ${colorClasses.icon}`}>
              {icon}
            </div>
          </div>
        </div>

        {/* Change Indicator */}
        {change && (
          <div className="flex items-center gap-2 mb-3">
            <Badge 
              variant="secondary" 
              className={`flex items-center gap-1 text-xs ${getChangeColor(change.type)}`}
            >
              {getChangeIcon(change.type)}
              {Math.abs(change.value)}%
            </Badge>
            <span className="text-xs text-gray-500 arabic-text">
              {change.period}
            </span>
          </div>
        )}

        {/* Progress Bar */}
        {progress && (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-600 dark:text-gray-400 arabic-text">
                {progress.label || 'التقدم'}
              </span>
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {progress.value}/{progress.max}
              </span>
            </div>
            <Progress 
              value={(progress.value / progress.max) * 100} 
              className="h-2"
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// مكون مجموعة الإحصائيات
interface StatsGridProps {
  stats: Array<Omit<StatsCardProps, 'className'>>
  columns?: 2 | 3 | 4 | 6
  className?: string
}

export function StatsGrid({ 
  stats, 
  columns = 4, 
  className = "" 
}: StatsGridProps) {
  const getGridCols = (cols: number) => {
    switch (cols) {
      case 2: return 'grid-cols-1 md:grid-cols-2'
      case 3: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      case 6: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
      default: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
    }
  }

  return (
    <div className={`grid ${getGridCols(columns)} gap-6 ${className}`}>
      {stats.map((stat, index) => (
        <StatsCard key={index} {...stat} />
      ))}
    </div>
  )
}

// مكون إحصائية مبسطة
interface SimpleStatProps {
  label: string
  value: string | number
  icon: React.ReactNode
  color?: string
}

export function SimpleStat({ 
  label, 
  value, 
  icon, 
  color = 'text-blue-600' 
}: SimpleStatProps) {
  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div className={`${color}`}>
        {icon}
      </div>
      <div>
        <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
          {label}
        </p>
        <p className="font-semibold text-gray-900 dark:text-white">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </p>
      </div>
    </div>
  )
}

// مكون مقارنة الإحصائيات
interface StatComparisonProps {
  current: {
    label: string
    value: number
    period: string
  }
  previous: {
    label: string
    value: number
    period: string
  }
  format?: (value: number) => string
}

export function StatComparison({ 
  current, 
  previous, 
  format = (v) => v.toString() 
}: StatComparisonProps) {
  const change = current.value - previous.value
  const changePercent = previous.value > 0 ? (change / previous.value) * 100 : 0
  const isIncrease = change > 0
  const isDecrease = change < 0

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
              {current.label}
            </p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {format(current.value)}
            </p>
            <p className="text-xs text-gray-500 arabic-text">
              {current.period}
            </p>
          </div>

          <div className="flex items-center justify-between pt-3 border-t">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                {previous.label}
              </p>
              <p className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {format(previous.value)}
              </p>
            </div>

            <div className="text-right">
              <div className={`flex items-center gap-1 ${
                isIncrease ? 'text-green-600' : 
                isDecrease ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {isIncrease && <TrendingUp className="h-4 w-4" />}
                {isDecrease && <TrendingDown className="h-4 w-4" />}
                {!isIncrease && !isDecrease && <Minus className="h-4 w-4" />}
                <span className="font-medium">
                  {Math.abs(changePercent).toFixed(1)}%
                </span>
              </div>
              <p className="text-xs text-gray-500 arabic-text">
                {isIncrease ? 'زيادة' : isDecrease ? 'نقصان' : 'ثابت'}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
