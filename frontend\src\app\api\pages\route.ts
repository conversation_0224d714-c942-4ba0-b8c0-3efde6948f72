import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

// GET - جلب جميع الصفحات
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    const { searchParams } = new URL(request.url)
    const includeUnpublished = searchParams.get('include_unpublished') === 'true'
    const language = searchParams.get('language') || 'ar'

    // التحقق من صلاحيات الأدمن للصفحات غير المنشورة
    let canViewUnpublished = false
    if (includeUnpublished) {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single()
        
        canViewUnpublished = profile?.role === 'admin'
      }
    }

    let query = supabase
      .from('pages')
      .select(`
        *,
        page_content!inner(
          title,
          meta_description,
          language
        ),
        profiles(full_name)
      `)
      .eq('page_content.language', language)
      .order('created_at', { ascending: false })

    // تطبيق فلتر النشر
    if (!canViewUnpublished) {
      query = query.eq('is_published', true)
    }

    const { data: pages, error } = await query

    if (error) {
      console.error('Error fetching pages:', error)
      return NextResponse.json(
        { error: 'فشل في جلب الصفحات' },
        { status: 500 }
      )
    }

    return NextResponse.json({ pages })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - إضافة صفحة جديدة
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      slug,
      is_published,
      featured_image,
      content // { ar: {title, content, meta_description}, en: {...}, fr: {...} }
    } = body

    // التحقق من البيانات المطلوبة
    if (!slug || !content || !content.ar || !content.ar.title || !content.ar.content) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار الـ slug
    const { data: existingPage } = await supabase
      .from('pages')
      .select('id')
      .eq('slug', slug)
      .single()

    if (existingPage) {
      return NextResponse.json(
        { error: 'الرابط المختصر موجود بالفعل' },
        { status: 400 }
      )
    }

    // إدراج الصفحة في قاعدة البيانات
    const { data: page, error: insertError } = await supabase
      .from('pages')
      .insert({
        slug,
        is_published: is_published ?? false,
        featured_image: featured_image || null,
        author_id: user.id
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting page:', insertError)
      return NextResponse.json(
        { error: 'فشل في إضافة الصفحة' },
        { status: 500 }
      )
    }

    // إدراج محتوى الصفحة بجميع اللغات
    const contentInserts = []
    for (const [language, langContent] of Object.entries(content)) {
      if (langContent && typeof langContent === 'object' && langContent.title && langContent.content) {
        contentInserts.push({
          page_id: page.id,
          language: language as 'ar' | 'en' | 'fr',
          title: langContent.title,
          content: langContent.content,
          meta_description: langContent.meta_description || null,
          meta_keywords: langContent.meta_keywords || null
        })
      }
    }

    if (contentInserts.length > 0) {
      const { error: contentError } = await supabase
        .from('page_content')
        .insert(contentInserts)

      if (contentError) {
        // حذف الصفحة في حالة فشل إدراج المحتوى
        await supabase.from('pages').delete().eq('id', page.id)
        console.error('Error inserting page content:', contentError)
        return NextResponse.json(
          { error: 'فشل في إضافة محتوى الصفحة' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({ 
      message: 'تم إضافة الصفحة بنجاح',
      page 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
