import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

// GET - جلب جميع المنتجات
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const available = searchParams.get('available')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    let query = supabase
      .from('products')
      .select('*')
      .order('created_at', { ascending: false })

    // تطبيق الفلاتر
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    if (available === 'true') {
      query = query.eq('is_available', true)
    } else if (available === 'false') {
      query = query.eq('is_available', false)
    }

    // تطبيق التصفح
    if (limit) {
      query = query.limit(parseInt(limit))
    }

    if (offset) {
      query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10') - 1))
    }

    const { data: products, error } = await query

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json(
        { error: 'فشل في جلب المنتجات' },
        { status: 500 }
      )
    }

    return NextResponse.json({ products })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - إضافة منتج جديد
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات المستخدم
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // التحقق من دور المستخدم
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بإضافة المنتجات' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      category,
      price,
      rental_price,
      colors,
      sizes,
      images,
      stock_quantity,
      is_available,
      features,
      specifications
    } = body

    // التحقق من البيانات المطلوبة
    if (!name || !description || !category || !price || !colors || !sizes) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // إدراج المنتج في قاعدة البيانات
    const { data: product, error: insertError } = await supabase
      .from('products')
      .insert({
        name,
        description,
        category,
        price: parseFloat(price),
        rental_price: rental_price ? parseFloat(rental_price) : null,
        colors,
        sizes,
        images: images || [],
        stock_quantity: parseInt(stock_quantity) || 0,
        is_available: is_available ?? true,
        features: features || [],
        specifications: specifications || {}
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting product:', insertError)
      return NextResponse.json(
        { error: 'فشل في إضافة المنتج' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم إضافة المنتج بنجاح',
      product 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث منتج موجود
export async function PUT(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات المستخدم
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // التحقق من دور المستخدم
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بتحديث المنتجات' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      )
    }

    // تحديث المنتج
    const { data: product, error: updateError } = await supabase
      .from('products')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating product:', updateError)
      return NextResponse.json(
        { error: 'فشل في تحديث المنتج' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم تحديث المنتج بنجاح',
      product 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف منتج
export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات المستخدم
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // التحقق من دور المستخدم
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بحذف المنتجات' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      )
    }

    // حذف المنتج
    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting product:', deleteError)
      return NextResponse.json(
        { error: 'فشل في حذف المنتج' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم حذف المنتج بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
