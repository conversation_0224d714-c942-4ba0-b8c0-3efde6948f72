#!/usr/bin/env node

// Performance testing script
const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Starting Performance Tests...\n')

// Test configurations
const testConfigs = [
  {
    name: 'Build Performance',
    command: 'npm run build',
    timeout: 300000, // 5 minutes
    expectedTime: 120000, // 2 minutes
  },
  {
    name: 'Type Check Performance',
    command: 'npm run type-check',
    timeout: 60000, // 1 minute
    expectedTime: 30000, // 30 seconds
  },
  {
    name: 'Lint Performance',
    command: 'npm run lint',
    timeout: 60000, // 1 minute
    expectedTime: 20000, // 20 seconds
  }
]

// Bundle size analysis
function analyzeBundleSize() {
  console.log('📦 Analyzing Bundle Size...')
  
  const buildDir = path.join(__dirname, '../.next')
  if (!fs.existsSync(buildDir)) {
    console.log('❌ Build directory not found. Run npm run build first.')
    return
  }

  try {
    // Get build info
    const buildManifest = path.join(buildDir, 'build-manifest.json')
    if (fs.existsSync(buildManifest)) {
      const manifest = JSON.parse(fs.readFileSync(buildManifest, 'utf8'))
      console.log('✅ Build manifest found')
      
      // Analyze main bundle sizes
      const staticDir = path.join(buildDir, 'static')
      if (fs.existsSync(staticDir)) {
        const jsDir = path.join(staticDir, 'chunks')
        if (fs.existsSync(jsDir)) {
          const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'))
          
          let totalSize = 0
          const fileSizes = jsFiles.map(file => {
            const filePath = path.join(jsDir, file)
            const stats = fs.statSync(filePath)
            totalSize += stats.size
            return {
              file,
              size: stats.size,
              sizeKB: Math.round(stats.size / 1024),
              sizeMB: Math.round(stats.size / 1024 / 1024 * 100) / 100
            }
          })

          // Sort by size
          fileSizes.sort((a, b) => b.size - a.size)

          console.log('\n📊 Bundle Analysis:')
          console.log(`Total JS Size: ${Math.round(totalSize / 1024)} KB`)
          console.log('\nLargest Files:')
          fileSizes.slice(0, 10).forEach(({ file, sizeKB }) => {
            console.log(`  ${file}: ${sizeKB} KB`)
          })

          // Check for large bundles
          const largeBundles = fileSizes.filter(f => f.sizeKB > 500)
          if (largeBundles.length > 0) {
            console.log('\n⚠️  Large bundles detected (>500KB):')
            largeBundles.forEach(({ file, sizeKB }) => {
              console.log(`  ${file}: ${sizeKB} KB`)
            })
          }

          // Recommendations
          console.log('\n💡 Recommendations:')
          if (totalSize > 1024 * 1024) { // > 1MB
            console.log('  - Consider code splitting for large bundles')
            console.log('  - Use dynamic imports for non-critical code')
            console.log('  - Analyze bundle with @next/bundle-analyzer')
          } else {
            console.log('  - Bundle size is within acceptable limits')
          }
        }
      }
    }
  } catch (error) {
    console.log('❌ Error analyzing bundle:', error.message)
  }
}

// Memory usage test
function testMemoryUsage() {
  console.log('\n🧠 Testing Memory Usage...')
  
  const initialMemory = process.memoryUsage()
  console.log('Initial Memory Usage:')
  console.log(`  RSS: ${Math.round(initialMemory.rss / 1024 / 1024)} MB`)
  console.log(`  Heap Used: ${Math.round(initialMemory.heapUsed / 1024 / 1024)} MB`)
  console.log(`  Heap Total: ${Math.round(initialMemory.heapTotal / 1024 / 1024)} MB`)

  // Simulate some operations
  const testData = []
  for (let i = 0; i < 10000; i++) {
    testData.push({
      id: i,
      data: 'test'.repeat(100),
      timestamp: new Date().toISOString()
    })
  }

  const afterTestMemory = process.memoryUsage()
  console.log('\nAfter Test Operations:')
  console.log(`  RSS: ${Math.round(afterTestMemory.rss / 1024 / 1024)} MB`)
  console.log(`  Heap Used: ${Math.round(afterTestMemory.heapUsed / 1024 / 1024)} MB`)
  console.log(`  Heap Total: ${Math.round(afterTestMemory.heapTotal / 1024 / 1024)} MB`)

  // Cleanup
  testData.length = 0
  global.gc && global.gc()

  const finalMemory = process.memoryUsage()
  console.log('\nAfter Cleanup:')
  console.log(`  RSS: ${Math.round(finalMemory.rss / 1024 / 1024)} MB`)
  console.log(`  Heap Used: ${Math.round(finalMemory.heapUsed / 1024 / 1024)} MB`)
  console.log(`  Heap Total: ${Math.round(finalMemory.heapTotal / 1024 / 1024)} MB`)

  // Check for memory leaks
  const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
  if (memoryIncrease > 50 * 1024 * 1024) { // > 50MB
    console.log('⚠️  Potential memory leak detected')
  } else {
    console.log('✅ Memory usage is normal')
  }
}

// Run performance tests
async function runPerformanceTests() {
  const results = []

  for (const config of testConfigs) {
    console.log(`\n🧪 Running ${config.name}...`)
    
    const startTime = Date.now()
    
    try {
      execSync(config.command, {
        stdio: 'pipe',
        timeout: config.timeout,
        cwd: path.join(__dirname, '..')
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      const result = {
        name: config.name,
        duration,
        expected: config.expectedTime,
        status: duration <= config.expectedTime ? 'PASS' : 'SLOW',
        improvement: duration > config.expectedTime ? 
          `${Math.round((duration - config.expectedTime) / 1000)}s slower than expected` : 
          `${Math.round((config.expectedTime - duration) / 1000)}s faster than expected`
      }
      
      results.push(result)
      
      console.log(`✅ ${config.name} completed in ${Math.round(duration / 1000)}s`)
      if (result.status === 'SLOW') {
        console.log(`⚠️  ${result.improvement}`)
      }
      
    } catch (error) {
      console.log(`❌ ${config.name} failed:`, error.message)
      results.push({
        name: config.name,
        duration: config.timeout,
        expected: config.expectedTime,
        status: 'FAIL',
        error: error.message
      })
    }
  }

  return results
}

// Generate performance report
function generateReport(results) {
  console.log('\n📊 Performance Test Report')
  console.log('=' .repeat(50))
  
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : 
                   result.status === 'SLOW' ? '⚠️' : '❌'
    
    console.log(`${status} ${result.name}`)
    console.log(`   Duration: ${Math.round(result.duration / 1000)}s`)
    console.log(`   Expected: ${Math.round(result.expected / 1000)}s`)
    if (result.improvement) {
      console.log(`   Note: ${result.improvement}`)
    }
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
    console.log()
  })

  // Overall assessment
  const passCount = results.filter(r => r.status === 'PASS').length
  const slowCount = results.filter(r => r.status === 'SLOW').length
  const failCount = results.filter(r => r.status === 'FAIL').length

  console.log('📈 Summary:')
  console.log(`   Passed: ${passCount}`)
  console.log(`   Slow: ${slowCount}`)
  console.log(`   Failed: ${failCount}`)

  if (failCount === 0 && slowCount === 0) {
    console.log('\n🎉 All performance tests passed!')
  } else if (failCount === 0) {
    console.log('\n⚠️  Some tests are slower than expected')
  } else {
    console.log('\n❌ Some tests failed')
  }

  // Recommendations
  console.log('\n💡 Performance Recommendations:')
  if (slowCount > 0 || failCount > 0) {
    console.log('   - Consider optimizing build process')
    console.log('   - Check for large dependencies')
    console.log('   - Use build caching')
    console.log('   - Optimize TypeScript configuration')
  } else {
    console.log('   - Performance is good!')
    console.log('   - Continue monitoring with CI/CD')
  }
}

// Main execution
async function main() {
  try {
    // Run performance tests
    const results = await runPerformanceTests()
    
    // Analyze bundle size
    analyzeBundleSize()
    
    // Test memory usage
    testMemoryUsage()
    
    // Generate report
    generateReport(results)
    
    console.log('\n🏁 Performance testing completed!')
    
  } catch (error) {
    console.error('❌ Performance testing failed:', error)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = {
  runPerformanceTests,
  analyzeBundleSize,
  testMemoryUsage,
  generateReport
}
