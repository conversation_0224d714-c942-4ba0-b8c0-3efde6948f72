"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { 
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  Heart,
  Gift,
  CreditCard,
  Truck,
  Shield,
  ArrowRight,
  ArrowLeft,
  Tag,
  Percent
} from 'lucide-react'

// أنواع البيانات
interface CartItem {
  id: string
  name: string
  description: string
  image: string
  price: number
  originalPrice?: number
  quantity: number
  size?: string
  color?: string
  customizations?: any
  category: 'gown' | 'cap' | 'accessories'
  inStock: boolean
}

interface PromoCode {
  code: string
  discount: number
  type: 'percentage' | 'fixed'
  minAmount?: number
}

export default function CartPage() {
  const { user, profile } = useAuth()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [promoCode, setPromoCode] = useState('')
  const [appliedPromo, setAppliedPromo] = useState<PromoCode | null>(null)
  const [loading, setLoading] = useState(true)

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockCartItems: CartItem[] = [
      {
        id: '1',
        name: 'زي التخرج الكلاسيكي',
        description: 'زي تخرج أنيق مصنوع من أجود الخامات',
        image: '/api/placeholder/150/150',
        price: 299.99,
        originalPrice: 349.99,
        quantity: 1,
        size: 'L',
        color: 'أسود',
        category: 'gown',
        inStock: true,
        customizations: {
          embroidery: 'اسم الطالب',
          specialRequests: 'تطريز ذهبي'
        }
      },
      {
        id: '2',
        name: 'قبعة التخرج المميزة',
        description: 'قبعة تخرج مع شرابة ذهبية',
        image: '/api/placeholder/150/150',
        price: 89.99,
        quantity: 1,
        color: 'أسود',
        category: 'cap',
        inStock: true
      },
      {
        id: '3',
        name: 'وشاح التخرج الفاخر',
        description: 'وشاح مطرز بتصميم مميز',
        image: '/api/placeholder/150/150',
        price: 149.99,
        quantity: 1,
        color: 'ذهبي',
        category: 'accessories',
        inStock: false
      }
    ]

    setCartItems(mockCartItems)
    setLoading(false)
  }, [])

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return
    setCartItems(items =>
      items.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    )
  }

  const removeItem = (id: string) => {
    setCartItems(items => items.filter(item => item.id !== id))
  }

  const applyPromoCode = () => {
    // محاكاة التحقق من كود الخصم
    const validCodes: PromoCode[] = [
      { code: 'GRAD2024', discount: 15, type: 'percentage', minAmount: 200 },
      { code: 'WELCOME50', discount: 50, type: 'fixed', minAmount: 100 }
    ]

    const validCode = validCodes.find(code => 
      code.code.toLowerCase() === promoCode.toLowerCase()
    )

    if (validCode && subtotal >= (validCode.minAmount || 0)) {
      setAppliedPromo(validCode)
      setPromoCode('')
    } else {
      alert('كود الخصم غير صالح أو لا يحقق الحد الأدنى للمبلغ')
    }
  }

  const removePromoCode = () => {
    setAppliedPromo(null)
  }

  // حساب الإجماليات
  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  const shipping = subtotal > 500 ? 0 : 25 // شحن مجاني للطلبات أكثر من 500 درهم
  const discount = appliedPromo 
    ? appliedPromo.type === 'percentage' 
      ? (subtotal * appliedPromo.discount / 100)
      : appliedPromo.discount
    : 0
  const tax = (subtotal - discount) * 0.05 // ضريبة 5%
  const total = subtotal - discount + shipping + tax

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    )
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <ShoppingCart className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
              سلة التسوق فارغة
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 arabic-text">
              لم تقم بإضافة أي منتجات إلى سلة التسوق بعد
            </p>
            <Button asChild>
              <a href="/catalog" className="arabic-text">
                تصفح المنتجات
              </a>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            سلة التسوق 🛒
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            راجع منتجاتك واكمل عملية الشراء
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-6">
            {cartItems.map((item) => (
              <Card key={item.id}>
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {/* Product Image */}
                    <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0">
                      <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"></div>
                    </div>

                    {/* Product Details */}
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white arabic-text">
                            {item.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {item.description}
                          </p>
                        </div>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-red-500 hover:text-red-700 p-1"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>

                      {/* Product Options */}
                      <div className="flex gap-4 mb-3">
                        {item.size && (
                          <Badge variant="outline" className="arabic-text">
                            المقاس: {item.size}
                          </Badge>
                        )}
                        {item.color && (
                          <Badge variant="outline" className="arabic-text">
                            اللون: {item.color}
                          </Badge>
                        )}
                        {!item.inStock && (
                          <Badge variant="destructive" className="arabic-text">
                            غير متوفر
                          </Badge>
                        )}
                      </div>

                      {/* Customizations */}
                      {item.customizations && (
                        <div className="mb-3">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 arabic-text">
                            التخصيصات:
                          </p>
                          <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {Object.entries(item.customizations).map(([key, value]) => (
                              <span key={key} className="block">
                                {key}: {value as string}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Price and Quantity */}
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <Minus className="h-4 w-4" />
                          </button>
                          <span className="w-8 text-center font-medium">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <Plus className="h-4 w-4" />
                          </button>
                        </div>

                        <div className="text-right">
                          {item.originalPrice && (
                            <p className="text-sm text-gray-500 line-through">
                              {item.originalPrice} درهم
                            </p>
                          )}
                          <p className="text-lg font-bold text-gray-900 dark:text-white">
                            {(item.price * item.quantity).toFixed(2)} درهم
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Promo Code */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 arabic-text">
                  <Tag className="h-5 w-5" />
                  كود الخصم
                </CardTitle>
              </CardHeader>
              <CardContent>
                {appliedPromo ? (
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Percent className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-800 dark:text-green-200 arabic-text">
                        {appliedPromo.code}
                      </span>
                      <Badge variant="secondary">
                        {appliedPromo.type === 'percentage' 
                          ? `${appliedPromo.discount}%` 
                          : `${appliedPromo.discount} درهم`
                        }
                      </Badge>
                    </div>
                    <button
                      onClick={removePromoCode}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <Input
                      placeholder="أدخل كود الخصم"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      className="arabic-text"
                    />
                    <Button 
                      variant="outline" 
                      onClick={applyPromoCode}
                      disabled={!promoCode.trim()}
                    >
                      تطبيق
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle className="arabic-text">ملخص الطلب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Subtotal */}
                <div className="flex justify-between">
                  <span className="arabic-text">المجموع الفرعي:</span>
                  <span>{subtotal.toFixed(2)} درهم</span>
                </div>

                {/* Discount */}
                {discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span className="arabic-text">الخصم:</span>
                    <span>-{discount.toFixed(2)} درهم</span>
                  </div>
                )}

                {/* Shipping */}
                <div className="flex justify-between">
                  <span className="arabic-text">الشحن:</span>
                  <span>
                    {shipping === 0 ? (
                      <span className="text-green-600 arabic-text">مجاني</span>
                    ) : (
                      `${shipping} درهم`
                    )}
                  </span>
                </div>

                {/* Tax */}
                <div className="flex justify-between">
                  <span className="arabic-text">الضريبة (5%):</span>
                  <span>{tax.toFixed(2)} درهم</span>
                </div>

                <Separator />

                {/* Total */}
                <div className="flex justify-between text-lg font-bold">
                  <span className="arabic-text">الإجمالي:</span>
                  <span>{total.toFixed(2)} درهم</span>
                </div>

                {/* Shipping Info */}
                <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Truck className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium arabic-text">معلومات الشحن</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                    {shipping === 0 
                      ? 'شحن مجاني للطلبات أكثر من 500 درهم'
                      : 'التوصيل خلال 2-3 أيام عمل'
                    }
                  </p>
                </div>

                {/* Security Info */}
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Shield className="h-4 w-4" />
                  <span className="arabic-text">دفع آمن ومضمون</span>
                </div>

                {/* Checkout Button */}
                <Button className="w-full" size="lg" asChild>
                  <a href="/checkout" className="arabic-text">
                    متابعة للدفع
                    <ArrowLeft className="h-4 w-4 mr-2" />
                  </a>
                </Button>

                {/* Continue Shopping */}
                <Button variant="outline" className="w-full arabic-text" asChild>
                  <a href="/catalog">
                    <ArrowRight className="h-4 w-4 ml-2" />
                    متابعة التسوق
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
