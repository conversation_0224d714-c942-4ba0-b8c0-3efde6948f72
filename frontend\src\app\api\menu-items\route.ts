import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockMenuItem } from '@/lib/mockData'

// GET - جلب جميع عناصر القائمة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('include_inactive') === 'true'
    const parentId = searchParams.get('parent_id')

    // جلب البيانات الوهمية
    let menuItems = MockDataManager.getMenuItems()

    // تطبيق الفلاتر
    if (!includeInactive) {
      menuItems = menuItems.filter(item => item.is_active)
    }

    if (parentId) {
      if (parentId === 'null') {
        menuItems = menuItems.filter(item => !item.parent_id)
      } else {
        menuItems = menuItems.filter(item => item.parent_id === parentId)
      }
    }

    // ترتيب حسب order_index
    menuItems.sort((a, b) => a.order_index - b.order_index)

    return NextResponse.json({ 
      menuItems,
      total: menuItems.length
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - إضافة عنصر قائمة جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title_ar,
      title_en,
      title_fr,
      slug,
      icon,
      parent_id,
      order_index,
      is_active,
      target_type,
      target_value
    } = body

    // التحقق من البيانات المطلوبة
    if (!title_ar || !slug || !target_type || !target_value) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // جلب العناصر الحالية
    const menuItems = MockDataManager.getMenuItems()

    // التحقق من عدم تكرار الـ slug
    const existingItem = menuItems.find(item => item.slug === slug)
    if (existingItem) {
      return NextResponse.json(
        { error: 'الرابط المختصر موجود بالفعل' },
        { status: 400 }
      )
    }

    // إنشاء العنصر الجديد
    const newMenuItem: MockMenuItem = {
      id: MockDataManager.generateId(),
      title_ar,
      title_en: title_en || undefined,
      title_fr: title_fr || undefined,
      slug,
      icon: icon || undefined,
      parent_id: parent_id || undefined,
      order_index: order_index || 0,
      is_active: is_active ?? true,
      target_type,
      target_value,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // حفظ العنصر
    menuItems.push(newMenuItem)
    MockDataManager.saveMenuItems(menuItems)

    return NextResponse.json({ 
      message: 'تم إضافة عنصر القائمة بنجاح',
      menuItem: newMenuItem 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
