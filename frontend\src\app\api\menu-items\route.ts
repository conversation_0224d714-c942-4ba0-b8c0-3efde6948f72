import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

// GET - جلب جميع عناصر القائمة
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('include_inactive') === 'true'
    const parentId = searchParams.get('parent_id')

    let query = supabase
      .from('menu_items')
      .select('*')
      .order('order_index', { ascending: true })

    // تطبيق الفلاتر
    if (!includeInactive) {
      query = query.eq('is_active', true)
    }

    if (parentId) {
      if (parentId === 'null') {
        query = query.is('parent_id', null)
      } else {
        query = query.eq('parent_id', parentId)
      }
    }

    const { data: menuItems, error } = await query

    if (error) {
      console.error('Error fetching menu items:', error)
      // إرجاع قائمة فارغة بدلاً من خطأ لتجنب كسر الواجهة
      return NextResponse.json({
        menuItems: [],
        warning: 'فشل في جلب عناصر القائمة من قاعدة البيانات'
      })
    }

    return NextResponse.json({ menuItems: menuItems || [] })
  } catch (error) {
    console.error('Unexpected error:', error)
    // إرجاع قائمة فارغة بدلاً من خطأ لتجنب كسر الواجهة
    return NextResponse.json({
      menuItems: [],
      warning: 'خطأ غير متوقع في جلب عناصر القائمة'
    })
  }
}

// POST - إضافة عنصر قائمة جديد
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      title_ar,
      title_en,
      title_fr,
      slug,
      icon,
      parent_id,
      order_index,
      is_active,
      target_type,
      target_value
    } = body

    // التحقق من البيانات المطلوبة
    if (!title_ar || !slug || !target_type || !target_value) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار الـ slug
    const { data: existingItem } = await supabase
      .from('menu_items')
      .select('id')
      .eq('slug', slug)
      .single()

    if (existingItem) {
      return NextResponse.json(
        { error: 'الرابط المختصر موجود بالفعل' },
        { status: 400 }
      )
    }

    // إدراج عنصر القائمة في قاعدة البيانات
    const { data: menuItem, error: insertError } = await supabase
      .from('menu_items')
      .insert({
        title_ar,
        title_en: title_en || null,
        title_fr: title_fr || null,
        slug,
        icon: icon || null,
        parent_id: parent_id || null,
        order_index: order_index || 0,
        is_active: is_active ?? true,
        target_type,
        target_value
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting menu item:', insertError)
      return NextResponse.json(
        { error: 'فشل في إضافة عنصر القائمة' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم إضافة عنصر القائمة بنجاح',
      menuItem 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث ترتيب عناصر القائمة
export async function PUT(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { items } = body

    if (!Array.isArray(items)) {
      return NextResponse.json(
        { error: 'البيانات غير صحيحة' },
        { status: 400 }
      )
    }

    // تحديث ترتيب العناصر
    const updates = items.map((item, index) => 
      supabase
        .from('menu_items')
        .update({ order_index: index + 1 })
        .eq('id', item.id)
    )

    await Promise.all(updates)

    return NextResponse.json({ 
      message: 'تم تحديث ترتيب القائمة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
