import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

// GET - جلب صفحة واحدة مع محتواها
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'ar'

    // جلب الصفحة مع محتواها
    const { data: page, error } = await supabase
      .from('pages')
      .select(`
        *,
        page_content(
          language,
          title,
          content,
          meta_description,
          meta_keywords
        ),
        profiles(full_name)
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'الصفحة غير موجودة' },
          { status: 404 }
        )
      }
      console.error('Error fetching page:', error)
      return NextResponse.json(
        { error: 'فشل في جلب الصفحة' },
        { status: 500 }
      )
    }

    // التحقق من صلاحية عرض الصفحة غير المنشورة
    if (!page.is_published) {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        return NextResponse.json(
          { error: 'الصفحة غير متاحة' },
          { status: 404 }
        )
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (!profile || profile.role !== 'admin') {
        return NextResponse.json(
          { error: 'الصفحة غير متاحة' },
          { status: 404 }
        )
      }
    }

    return NextResponse.json({ page })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث صفحة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      slug,
      is_published,
      featured_image,
      content // { ar: {title, content, meta_description}, en: {...}, fr: {...} }
    } = body

    // التحقق من البيانات المطلوبة
    if (!slug || !content || !content.ar || !content.ar.title || !content.ar.content) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار الـ slug (باستثناء الصفحة الحالية)
    const { data: existingPage } = await supabase
      .from('pages')
      .select('id')
      .eq('slug', slug)
      .neq('id', params.id)
      .single()

    if (existingPage) {
      return NextResponse.json(
        { error: 'الرابط المختصر موجود بالفعل' },
        { status: 400 }
      )
    }

    // تحديث الصفحة
    const { data: page, error: updateError } = await supabase
      .from('pages')
      .update({
        slug,
        is_published: is_published ?? false,
        featured_image: featured_image || null
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating page:', updateError)
      return NextResponse.json(
        { error: 'فشل في تحديث الصفحة' },
        { status: 500 }
      )
    }

    // حذف المحتوى القديم
    await supabase
      .from('page_content')
      .delete()
      .eq('page_id', params.id)

    // إدراج المحتوى الجديد
    const contentInserts = []
    for (const [language, langContent] of Object.entries(content)) {
      if (langContent && typeof langContent === 'object' && langContent.title && langContent.content) {
        contentInserts.push({
          page_id: params.id,
          language: language as 'ar' | 'en' | 'fr',
          title: langContent.title,
          content: langContent.content,
          meta_description: langContent.meta_description || null,
          meta_keywords: langContent.meta_keywords || null
        })
      }
    }

    if (contentInserts.length > 0) {
      const { error: contentError } = await supabase
        .from('page_content')
        .insert(contentInserts)

      if (contentError) {
        console.error('Error updating page content:', contentError)
        return NextResponse.json(
          { error: 'فشل في تحديث محتوى الصفحة' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({ 
      message: 'تم تحديث الصفحة بنجاح',
      page 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف صفحة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات الأدمن
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      )
    }

    // التحقق من عدم ارتباط الصفحة بعناصر القائمة
    const { data: menuItems } = await supabase
      .from('menu_items')
      .select('id')
      .eq('target_type', 'page')
      .eq('target_value', params.id)

    if (menuItems && menuItems.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف صفحة مرتبطة بعناصر القائمة' },
        { status: 400 }
      )
    }

    // حذف الصفحة (سيتم حذف المحتوى تلقائياً بسبب CASCADE)
    const { error: deleteError } = await supabase
      .from('pages')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting page:', deleteError)
      return NextResponse.json(
        { error: 'فشل في حذف الصفحة' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'تم حذف الصفحة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
