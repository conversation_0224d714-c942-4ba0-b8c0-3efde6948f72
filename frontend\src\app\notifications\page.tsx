"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useNotifications, NotificationType, NotificationPriority, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Bell,
  Search,
  Filter,
  CheckCheck,
  Trash2,
  Settings,
  ExternalLink,
  Calendar,
  Package,
  CreditCard,
  Gift,
  MessageSquare,
  Star,
  AlertTriangle
} from 'lucide-react'

export default function NotificationsPage() {
  const { user, profile } = useAuth()
  const { 
    notifications, 
    unreadCount, 
    mark<PERSON>Read, 
    markAllAsRead, 
    removeNotification, 
    clearAll,
    getNotificationsByType,
    getUnreadNotifications
  } = useNotifications()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<NotificationType | 'all'>('all')
  const [selectedPriority, setSelectedPriority] = useState<NotificationPriority | 'all'>('all')

  // فلترة الإشعارات
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || notification.type === selectedType
    const matchesPriority = selectedPriority === 'all' || notification.priority === selectedPriority
    
    return matchesSearch && matchesType && matchesPriority
  })

  // إحصائيات الإشعارات
  const stats = {
    total: notifications.length,
    unread: unreadCount,
    orders: getNotificationsByType('order_confirmed').length + 
            getNotificationsByType('order_shipped').length + 
            getNotificationsByType('order_delivered').length,
    payments: getNotificationsByType('payment_received').length + 
              getNotificationsByType('payment_failed').length,
    promotions: getNotificationsByType('promotion').length,
    system: getNotificationsByType('system').length
  }

  const notificationTypes = [
    { id: 'all', label: 'الكل', icon: <Bell className="h-4 w-4" />, count: stats.total },
    { id: 'order_confirmed', label: 'الطلبات', icon: <Package className="h-4 w-4" />, count: stats.orders },
    { id: 'payment_received', label: 'المدفوعات', icon: <CreditCard className="h-4 w-4" />, count: stats.payments },
    { id: 'promotion', label: 'العروض', icon: <Gift className="h-4 w-4" />, count: stats.promotions },
    { id: 'system', label: 'النظام', icon: <Settings className="h-4 w-4" />, count: stats.system }
  ]

  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {
    markAsRead(notificationId)
    if (actionUrl) {
      window.open(actionUrl, '_blank')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                الإشعارات 🔔
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                تابع جميع إشعاراتك ورسائلك
              </p>
            </div>
            <div className="flex gap-3">
              {unreadCount > 0 && (
                <Button onClick={markAllAsRead} className="arabic-text">
                  <CheckCheck className="h-4 w-4 mr-2" />
                  قراءة الكل ({unreadCount})
                </Button>
              )}
              <Button variant="outline" className="arabic-text">
                <Settings className="h-4 w-4 mr-2" />
                إعدادات الإشعارات
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي الإشعارات</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <Bell className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">غير مقروءة</p>
                  <p className="text-2xl font-bold text-red-600">{stats.unread}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إشعارات الطلبات</p>
                  <p className="text-2xl font-bold text-green-600">{stats.orders}</p>
                </div>
                <Package className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">العروض الترويجية</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.promotions}</p>
                </div>
                <Gift className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="arabic-text">البحث والفلترة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="البحث في الإشعارات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 arabic-text"
                  />
                </div>
              </div>

              {/* Type Filter */}
              <div className="flex gap-2 flex-wrap">
                {notificationTypes.map((type) => (
                  <Button
                    key={type.id}
                    variant={selectedType === type.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedType(type.id as any)}
                    className="arabic-text"
                  >
                    {type.icon}
                    <span className="mr-2">{type.label}</span>
                    {type.count > 0 && (
                      <Badge variant="secondary" className="mr-1">
                        {type.count}
                      </Badge>
                    )}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Main Notifications */}
          <div className="lg:col-span-2 space-y-4">
            {filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text">
                    لا توجد إشعارات
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 arabic-text">
                    {searchTerm ? 'لم يتم العثور على إشعارات تطابق البحث' : 'ستظهر إشعاراتك هنا'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredNotifications.map((notification) => (
                <Card 
                  key={notification.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    !notification.isRead ? 'ring-2 ring-blue-200 bg-blue-50/30 dark:bg-blue-900/10' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      {/* Icon */}
                      <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-lg ${
                        getNotificationColor(notification.priority)
                      }`}>
                        {getNotificationIcon(notification.type)}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className={`font-semibold arabic-text ${
                            !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {notification.title}
                          </h3>
                          
                          <div className="flex items-center gap-2 ml-4">
                            {!notification.isRead && (
                              <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                            )}
                            <Badge variant="outline" className={getNotificationColor(notification.priority)}>
                              {notification.priority === 'urgent' ? 'عاجل' :
                               notification.priority === 'high' ? 'عالي' :
                               notification.priority === 'medium' ? 'متوسط' : 'منخفض'}
                            </Badge>
                          </div>
                        </div>

                        <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text">
                          {notification.message}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatNotificationTime(notification.createdAt)}
                            </span>
                            
                            {notification.metadata?.orderId && (
                              <span className="flex items-center gap-1">
                                <Package className="h-4 w-4" />
                                {notification.metadata.orderId}
                              </span>
                            )}
                          </div>

                          <div className="flex items-center gap-2">
                            {notification.actionText && notification.actionUrl && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleNotificationClick(notification.id, notification.actionUrl)
                                }}
                                className="arabic-text"
                              >
                                {notification.actionText}
                                <ExternalLink className="h-3 w-3 mr-1" />
                              </Button>
                            )}
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                removeNotification(notification.id)
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Expiry Warning */}
                        {notification.expiresAt && (
                          <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
                            <p className="text-sm text-yellow-800 dark:text-yellow-200 arabic-text">
                              ⏰ ينتهي في {formatNotificationTime(notification.expiresAt)}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full arabic-text"
                  onClick={markAllAsRead}
                  disabled={unreadCount === 0}
                >
                  <CheckCheck className="h-4 w-4 mr-2" />
                  قراءة جميع الإشعارات
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full arabic-text"
                  onClick={clearAll}
                  disabled={notifications.length === 0}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  حذف جميع الإشعارات
                </Button>
                
                <Button variant="outline" className="w-full arabic-text">
                  <Settings className="h-4 w-4 mr-2" />
                  إعدادات الإشعارات
                </Button>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">النشاط الأخير</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getUnreadNotifications().slice(0, 5).map((notification) => (
                    <div key={notification.id} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="text-sm">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate arabic-text">
                          {notification.title}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatNotificationTime(notification.createdAt)}
                        </p>
                      </div>
                    </div>
                  ))}
                  
                  {getUnreadNotifications().length === 0 && (
                    <p className="text-sm text-gray-500 text-center arabic-text">
                      لا توجد إشعارات جديدة
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Notification Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إعدادات الإشعارات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm arabic-text">إشعارات الطلبات</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm arabic-text">العروض الترويجية</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm arabic-text">إشعارات النظام</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm arabic-text">إشعارات البريد الإلكتروني</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
