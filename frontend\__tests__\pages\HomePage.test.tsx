import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import HomePage from '@/app/page'
import { AuthProvider } from '@/contexts/AuthContext'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { ThemeProvider } from '@/components/theme-provider'

// Mock Next.js components
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => '/',
}))

jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => <img {...props} />,
}))

// Mock hooks
jest.mock('@/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    language: 'ar',
    setLanguage: jest.fn(),
  }),
}))

// Test wrapper
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider attribute="class" defaultTheme="light">
    <AuthProvider>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </AuthProvider>
  </ThemeProvider>
)

describe('HomePage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders hero section correctly', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for hero title
    expect(screen.getByText(/منصة أزياء التخرج الذكية/)).toBeInTheDocument()
    
    // Check for hero description
    expect(screen.getByText(/أول منصة مغربية متخصصة/)).toBeInTheDocument()
    
    // Check for CTA buttons
    expect(screen.getByText(/ابدأ التخصيص الآن/)).toBeInTheDocument()
    expect(screen.getByText(/تصفح الكتالوج/)).toBeInTheDocument()
  })

  test('displays features section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for features heading
    expect(screen.getByText(/لماذا تختار منصتنا؟/)).toBeInTheDocument()
    
    // Check for feature cards
    expect(screen.getByText(/تخصيص ذكي/)).toBeInTheDocument()
    expect(screen.getByText(/جودة عالية/)).toBeInTheDocument()
    expect(screen.getByText(/توصيل سريع/)).toBeInTheDocument()
    expect(screen.getByText(/دعم 24\/7/)).toBeInTheDocument()
  })

  test('shows popular products section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for products section
    expect(screen.getByText(/المنتجات الأكثر طلباً/)).toBeInTheDocument()
    
    // Check for product cards
    expect(screen.getByText(/زي التخرج الكلاسيكي/)).toBeInTheDocument()
    expect(screen.getByText(/قبعة التخرج المميزة/)).toBeInTheDocument()
    expect(screen.getByText(/وشاح التخرج الفاخر/)).toBeInTheDocument()
  })

  test('displays testimonials section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for testimonials heading
    expect(screen.getByText(/ماذا يقول عملاؤنا/)).toBeInTheDocument()
    
    // Check for testimonial content
    expect(screen.getByText(/تجربة رائعة ومنتجات عالية الجودة/)).toBeInTheDocument()
    expect(screen.getByText(/خدمة ممتازة وتوصيل سريع/)).toBeInTheDocument()
  })

  test('shows statistics section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for statistics
    expect(screen.getByText(/10,000\+/)).toBeInTheDocument()
    expect(screen.getByText(/طالب سعيد/)).toBeInTheDocument()
    expect(screen.getByText(/500\+/)).toBeInTheDocument()
    expect(screen.getByText(/مدرسة شريكة/)).toBeInTheDocument()
  })

  test('handles CTA button clicks', () => {
    const mockPush = jest.fn()
    jest.doMock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
        replace: jest.fn(),
        back: jest.fn(),
      }),
      usePathname: () => '/',
    }))

    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Click customize button
    const customizeButton = screen.getByText(/ابدأ التخصيص الآن/)
    fireEvent.click(customizeButton)
    
    // Should navigate to customize page
    expect(customizeButton.closest('a')).toHaveAttribute('href', '/customize')
  })

  test('displays newsletter signup', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for newsletter section
    expect(screen.getByText(/اشترك في نشرتنا الإخبارية/)).toBeInTheDocument()
    
    // Check for email input
    const emailInput = screen.getByPlaceholderText(/بريدك الإلكتروني/)
    expect(emailInput).toBeInTheDocument()
    
    // Check for subscribe button
    expect(screen.getByText(/اشتراك/)).toBeInTheDocument()
  })

  test('handles newsletter subscription', async () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    const emailInput = screen.getByPlaceholderText(/بريدك الإلكتروني/)
    const subscribeButton = screen.getByText(/اشتراك/)

    // Enter email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    // Click subscribe
    fireEvent.click(subscribeButton)

    // Should show success message (if implemented)
    await waitFor(() => {
      expect(emailInput).toHaveValue('<EMAIL>')
    })
  })

  test('is responsive on mobile devices', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })

    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check if mobile-specific classes are applied
    const heroSection = screen.getByText(/منصة أزياء التخرج الذكية/).closest('div')
    expect(heroSection).toBeInTheDocument()
  })

  test('loads images correctly', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for hero image
    const heroImages = screen.getAllByRole('img')
    expect(heroImages.length).toBeGreaterThan(0)
    
    // Check if images have proper alt text
    heroImages.forEach(img => {
      expect(img).toHaveAttribute('alt')
    })
  })

  test('has proper SEO elements', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for proper heading hierarchy
    const h1Elements = screen.getAllByRole('heading', { level: 1 })
    expect(h1Elements.length).toBeGreaterThan(0)
    
    const h2Elements = screen.getAllByRole('heading', { level: 2 })
    expect(h2Elements.length).toBeGreaterThan(0)
  })

  test('displays loading states correctly', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Page should render without loading indicators on initial load
    expect(screen.queryByText(/جاري التحميل/)).not.toBeInTheDocument()
  })
})

// Integration tests
describe('HomePage Integration', () => {
  test('integrates with authentication context', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Should render correctly regardless of auth state
    expect(screen.getByText(/منصة أزياء التخرج الذكية/)).toBeInTheDocument()
  })

  test('integrates with theme provider', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Should apply theme classes correctly
    const mainContent = screen.getByText(/منصة أزياء التخرج الذكية/).closest('main')
    expect(mainContent).toHaveClass('min-h-screen')
  })

  test('works with notification system', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Should not show any error notifications
    expect(screen.queryByText(/خطأ/)).not.toBeInTheDocument()
  })
})

// Accessibility tests
describe('HomePage Accessibility', () => {
  test('has proper ARIA labels and roles', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for main landmark
    const main = screen.getByRole('main')
    expect(main).toBeInTheDocument()

    // Check for proper button roles
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toBeInTheDocument()
    })

    // Check for proper link roles
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAttribute('href')
    })
  })

  test('supports keyboard navigation', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check if interactive elements are focusable
    const interactiveElements = screen.getAllByRole('button')
    interactiveElements.forEach(element => {
      expect(element).not.toHaveAttribute('tabindex', '-1')
    })
  })

  test('has proper color contrast', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check for proper text color classes
    const headings = screen.getAllByRole('heading')
    headings.forEach(heading => {
      expect(heading).toHaveClass(/text-/)
    })
  })
})

// Performance tests
describe('HomePage Performance', () => {
  test('renders within acceptable time', () => {
    const startTime = performance.now()
    
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render within 200ms
    expect(renderTime).toBeLessThan(200)
  })

  test('handles multiple re-renders efficiently', () => {
    const { rerender } = render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Re-render multiple times
    for (let i = 0; i < 5; i++) {
      rerender(
        <TestWrapper>
          <HomePage />
        </TestWrapper>
      )
    }

    // Should still be responsive
    expect(screen.getByText(/منصة أزياء التخرج الذكية/)).toBeInTheDocument()
  })

  test('optimizes image loading', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    )

    // Check if images have loading optimization
    const images = screen.getAllByRole('img')
    images.forEach(img => {
      // Next.js Image component should add optimization attributes
      expect(img).toBeInTheDocument()
    })
  })
})
