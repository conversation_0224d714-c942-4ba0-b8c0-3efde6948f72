// Performance monitoring utilities

interface PerformanceMetrics {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, any>
}

interface WebVitals {
  CLS: number // Cumulative Layout Shift
  FID: number // First Input Delay
  FCP: number // First Contentful Paint
  LCP: number // Largest Contentful Paint
  TTFB: number // Time to First Byte
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private observers: PerformanceObserver[] = []
  private vitals: Partial<WebVitals> = {}

  constructor() {
    this.initializeObservers()
  }

  // Start measuring performance
  startMeasure(name: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetrics = {
      name,
      startTime: performance.now(),
      metadata
    }
    
    this.metrics.push(metric)
    
    // Mark the start for Performance API
    performance.mark(`${name}-start`)
  }

  // End measuring performance
  endMeasure(name: string): number | null {
    const metric = this.metrics.find(m => m.name === name && !m.endTime)
    
    if (!metric) {
      console.warn(`Performance measure '${name}' not found or already ended`)
      return null
    }

    metric.endTime = performance.now()
    metric.duration = metric.endTime - metric.startTime

    // Mark the end and measure for Performance API
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)

    return metric.duration
  }

  // Measure a function execution time
  measureFunction<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    this.startMeasure(name, metadata)
    
    try {
      const result = fn()
      
      // Handle async functions
      if (result instanceof Promise) {
        return result.finally(() => {
          this.endMeasure(name)
        }) as T
      }
      
      this.endMeasure(name)
      return result
    } catch (error) {
      this.endMeasure(name)
      throw error
    }
  }

  // Measure async function execution time
  async measureAsync<T>(
    name: string, 
    fn: () => Promise<T>, 
    metadata?: Record<string, any>
  ): Promise<T> {
    this.startMeasure(name, metadata)
    
    try {
      const result = await fn()
      this.endMeasure(name)
      return result
    } catch (error) {
      this.endMeasure(name)
      throw error
    }
  }

  // Get all metrics
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  // Get metrics by name
  getMetricsByName(name: string): PerformanceMetrics[] {
    return this.metrics.filter(m => m.name === name)
  }

  // Get average duration for a metric
  getAverageDuration(name: string): number {
    const metrics = this.getMetricsByName(name).filter(m => m.duration !== undefined)
    
    if (metrics.length === 0) return 0
    
    const total = metrics.reduce((sum, m) => sum + (m.duration || 0), 0)
    return total / metrics.length
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics = []
  }

  // Initialize performance observers
  private initializeObservers(): void {
    if (typeof window === 'undefined') return

    // Observe navigation timing
    if ('PerformanceObserver' in window) {
      try {
        // Observe paint timing
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.vitals.FCP = entry.startTime
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)

        // Observe largest contentful paint
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.vitals.LCP = lastEntry.startTime
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)

        // Observe layout shift
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
          this.vitals.CLS = clsValue
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)

        // Observe first input delay
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.vitals.FID = (entry as any).processingStart - entry.startTime
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)

      } catch (error) {
        console.warn('Performance observers not supported:', error)
      }
    }
  }

  // Get Web Vitals
  getWebVitals(): Partial<WebVitals> {
    // Add TTFB from navigation timing
    if (typeof window !== 'undefined' && window.performance?.timing) {
      const timing = window.performance.timing
      this.vitals.TTFB = timing.responseStart - timing.requestStart
    }

    return { ...this.vitals }
  }

  // Check if performance is good
  isPerformanceGood(): boolean {
    const vitals = this.getWebVitals()
    
    return (
      (vitals.LCP || 0) < 2500 && // LCP should be < 2.5s
      (vitals.FID || 0) < 100 && // FID should be < 100ms
      (vitals.CLS || 0) < 0.1 && // CLS should be < 0.1
      (vitals.FCP || 0) < 1800 && // FCP should be < 1.8s
      (vitals.TTFB || 0) < 600 // TTFB should be < 600ms
    )
  }

  // Generate performance report
  generateReport(): {
    metrics: PerformanceMetrics[]
    webVitals: Partial<WebVitals>
    summary: {
      totalMeasures: number
      averageLoadTime: number
      slowestOperation: string | null
      performanceGrade: 'A' | 'B' | 'C' | 'D' | 'F'
    }
  } {
    const metrics = this.getMetrics()
    const webVitals = this.getWebVitals()
    
    const completedMetrics = metrics.filter(m => m.duration !== undefined)
    const averageLoadTime = completedMetrics.length > 0
      ? completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / completedMetrics.length
      : 0

    const slowestOperation = completedMetrics.length > 0
      ? completedMetrics.reduce((slowest, current) => 
          (current.duration || 0) > (slowest.duration || 0) ? current : slowest
        ).name
      : null

    // Calculate performance grade
    let grade: 'A' | 'B' | 'C' | 'D' | 'F' = 'A'
    const isGood = this.isPerformanceGood()
    const avgTime = averageLoadTime

    if (!isGood || avgTime > 3000) grade = 'F'
    else if (avgTime > 2000) grade = 'D'
    else if (avgTime > 1500) grade = 'C'
    else if (avgTime > 1000) grade = 'B'

    return {
      metrics,
      webVitals,
      summary: {
        totalMeasures: metrics.length,
        averageLoadTime,
        slowestOperation,
        performanceGrade: grade
      }
    }
  }

  // Cleanup observers
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// React hook for performance monitoring
export function usePerformanceMonitor() {
  return {
    startMeasure: performanceMonitor.startMeasure.bind(performanceMonitor),
    endMeasure: performanceMonitor.endMeasure.bind(performanceMonitor),
    measureFunction: performanceMonitor.measureFunction.bind(performanceMonitor),
    measureAsync: performanceMonitor.measureAsync.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
    getWebVitals: performanceMonitor.getWebVitals.bind(performanceMonitor),
    generateReport: performanceMonitor.generateReport.bind(performanceMonitor),
    isPerformanceGood: performanceMonitor.isPerformanceGood.bind(performanceMonitor)
  }
}

// Utility functions for common measurements
export const measurePageLoad = () => {
  if (typeof window === 'undefined') return

  performanceMonitor.startMeasure('page-load')
  
  window.addEventListener('load', () => {
    performanceMonitor.endMeasure('page-load')
  })
}

export const measureComponentRender = (componentName: string) => {
  return {
    start: () => performanceMonitor.startMeasure(`component-render-${componentName}`),
    end: () => performanceMonitor.endMeasure(`component-render-${componentName}`)
  }
}

export const measureAPICall = async <T>(
  apiName: string,
  apiCall: () => Promise<T>
): Promise<T> => {
  return performanceMonitor.measureAsync(`api-call-${apiName}`, apiCall)
}

// Performance decorator for class methods
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const measureName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measureFunction(measureName, () => {
        return originalMethod.apply(this, args)
      })
    }

    return descriptor
  }
}

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT: 1000, // < 1s
  GOOD: 2000, // < 2s
  FAIR: 3000, // < 3s
  POOR: 5000, // < 5s
  // > 5s is considered very poor
}

// Check if we're in development mode
export const isDevelopment = process.env.NODE_ENV === 'development'

// Log performance metrics in development
if (isDevelopment && typeof window !== 'undefined') {
  // Log performance report every 30 seconds in development
  setInterval(() => {
    const report = performanceMonitor.generateReport()
    console.group('🚀 Performance Report')
    console.log('Web Vitals:', report.webVitals)
    console.log('Summary:', report.summary)
    console.log('Recent Metrics:', report.metrics.slice(-10))
    console.groupEnd()
  }, 30000)
}
