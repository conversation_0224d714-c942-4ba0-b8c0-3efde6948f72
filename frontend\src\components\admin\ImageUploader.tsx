"use client"

import { useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Upload,
  X,
  Image as ImageIcon,
  FileImage,
  AlertCircle,
  Check,
  RotateCw,
  Move,
  Trash2
} from 'lucide-react'

interface ImageFile {
  file: File
  preview: string
  id: string
  uploading?: boolean
  uploaded?: boolean
  error?: string
}

interface ImageUploaderProps {
  images: ImageFile[]
  onImagesChange: (images: ImageFile[]) => void
  maxImages?: number
  maxSize?: number // بالبايت
  acceptedTypes?: string[]
  className?: string
}

export function ImageUploader({
  images,
  onImagesChange,
  maxImages = 10,
  maxSize = 5 * 1024 * 1024, // 5MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  className = ""
}: ImageUploaderProps) {
  const [dragActive, setDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})

  // التحقق من صحة الملف
  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return 'نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP'
    }

    if (file.size > maxSize) {
      return `حجم الملف كبير جداً. الحد الأقصى ${(maxSize / 1024 / 1024).toFixed(1)} ميجابايت`
    }

    return null
  }

  // معالجة رفع الملفات
  const handleFiles = useCallback((files: FileList) => {
    const newImages: ImageFile[] = []
    const currentCount = images.length

    Array.from(files).forEach((file, index) => {
      if (currentCount + newImages.length >= maxImages) {
        return
      }

      const error = validateFile(file)
      const id = `${Date.now()}-${index}`

      if (error) {
        newImages.push({
          file,
          preview: '',
          id,
          error
        })
        return
      }

      // إنشاء معاينة للصورة
      const reader = new FileReader()
      reader.onload = (e) => {
        const preview = e.target?.result as string
        newImages.push({
          file,
          preview,
          id,
          uploading: false,
          uploaded: false
        })

        if (newImages.length === Math.min(files.length, maxImages - currentCount)) {
          onImagesChange([...images, ...newImages])
        }
      }
      reader.readAsDataURL(file)
    })
  }, [images, maxImages, maxSize, acceptedTypes, onImagesChange])

  // معالجة السحب والإفلات
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }, [handleFiles])

  // معالجة اختيار الملفات
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files)
    }
  }

  // حذف صورة
  const removeImage = (id: string) => {
    const updatedImages = images.filter(img => img.id !== id)
    onImagesChange(updatedImages)
  }

  // تحريك صورة (ترتيب)
  const moveImage = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...images]
    const [movedImage] = updatedImages.splice(fromIndex, 1)
    updatedImages.splice(toIndex, 0, movedImage)
    onImagesChange(updatedImages)
  }

  // محاكاة رفع الصورة
  const simulateUpload = async (imageId: string) => {
    const imageIndex = images.findIndex(img => img.id === imageId)
    if (imageIndex === -1) return

    // تحديث حالة الرفع
    const updatedImages = [...images]
    updatedImages[imageIndex] = { ...updatedImages[imageIndex], uploading: true }
    onImagesChange(updatedImages)

    // محاكاة التقدم
    for (let progress = 0; progress <= 100; progress += 10) {
      setUploadProgress(prev => ({ ...prev, [imageId]: progress }))
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // تحديث حالة الانتهاء
    updatedImages[imageIndex] = { 
      ...updatedImages[imageIndex], 
      uploading: false, 
      uploaded: true 
    }
    onImagesChange(updatedImages)
    
    setUploadProgress(prev => {
      const newProgress = { ...prev }
      delete newProgress[imageId]
      return newProgress
    })
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
        } ${images.length >= maxImages ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <Upload className={`h-12 w-12 mx-auto mb-4 ${
          dragActive ? 'text-blue-500' : 'text-gray-400'
        }`} />
        
        <div className="space-y-2">
          <p className="text-lg font-medium arabic-text">
            {dragActive ? 'أفلت الصور هنا' : 'اسحب الصور هنا أو'}
          </p>
          
          {images.length < maxImages && (
            <label className="cursor-pointer">
              <input
                type="file"
                multiple
                accept={acceptedTypes.join(',')}
                className="hidden"
                onChange={handleInputChange}
                disabled={images.length >= maxImages}
              />
              <Button type="button" variant="outline" size="sm" disabled={images.length >= maxImages}>
                <FileImage className="h-4 w-4 mr-2" />
                اختر الصور
              </Button>
            </label>
          )}
        </div>

        <div className="mt-4 space-y-1">
          <p className="text-sm text-gray-500 arabic-text">
            يمكنك رفع حتى {maxImages} صور بحجم أقصى {(maxSize / 1024 / 1024).toFixed(1)} ميجابايت لكل صورة
          </p>
          <p className="text-xs text-gray-400">
            الصيغ المدعومة: {acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}
          </p>
        </div>

        {images.length >= maxImages && (
          <div className="mt-4 flex items-center justify-center gap-2 text-orange-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm arabic-text">تم الوصول للحد الأقصى من الصور</span>
          </div>
        )}
      </div>

      {/* عرض الصور المرفوعة */}
      {images.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium arabic-text">
              الصور المرفوعة ({images.length}/{maxImages})
            </h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onImagesChange([])}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              حذف الكل
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <Card key={image.id} className="relative group overflow-hidden">
                <CardContent className="p-0">
                  {image.error ? (
                    <div className="aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20">
                      <div className="text-center p-4">
                        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                        <p className="text-xs text-red-600 arabic-text">{image.error}</p>
                      </div>
                    </div>
                  ) : (
                    <>
                      <img
                        src={image.preview}
                        alt={`صورة ${index + 1}`}
                        className="w-full aspect-square object-cover"
                      />
                      
                      {/* شريط التقدم */}
                      {image.uploading && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[120px]">
                            <div className="flex items-center gap-2 mb-2">
                              <RotateCw className="h-4 w-4 animate-spin" />
                              <span className="text-sm arabic-text">جاري الرفع...</span>
                            </div>
                            <Progress value={uploadProgress[image.id] || 0} className="h-2" />
                          </div>
                        </div>
                      )}

                      {/* مؤشر الانتهاء */}
                      {image.uploaded && (
                        <div className="absolute top-2 left-2">
                          <Badge className="bg-green-600">
                            <Check className="h-3 w-3 mr-1" />
                            تم الرفع
                          </Badge>
                        </div>
                      )}

                      {/* أزرار التحكم */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex gap-1">
                          {!image.uploading && !image.uploaded && (
                            <Button
                              type="button"
                              size="sm"
                              variant="secondary"
                              onClick={() => simulateUpload(image.id)}
                              className="h-8 w-8 p-0"
                            >
                              <Upload className="h-3 w-3" />
                            </Button>
                          )}
                          <Button
                            type="button"
                            size="sm"
                            variant="destructive"
                            onClick={() => removeImage(image.id)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* مؤشر الصورة الرئيسية */}
                      {index === 0 && (
                        <Badge className="absolute bottom-2 left-2 bg-blue-600">
                          الصورة الرئيسية
                        </Badge>
                      )}

                      {/* أزرار الترتيب */}
                      <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex gap-1">
                          {index > 0 && (
                            <Button
                              type="button"
                              size="sm"
                              variant="secondary"
                              onClick={() => moveImage(index, index - 1)}
                              className="h-6 w-6 p-0"
                            >
                              ←
                            </Button>
                          )}
                          {index < images.length - 1 && (
                            <Button
                              type="button"
                              size="sm"
                              variant="secondary"
                              onClick={() => moveImage(index, index + 1)}
                              className="h-6 w-6 p-0"
                            >
                              →
                            </Button>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
