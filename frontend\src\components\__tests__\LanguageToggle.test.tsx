import { render, screen, fireEvent } from '@testing-library/react'
import { LanguageToggle } from '../language-toggle'
import { useTranslation } from '@/hooks/useTranslation'

// Mock the useTranslation hook
jest.mock('@/hooks/useTranslation')

const mockUseTranslation = useTranslation as jest.MockedFunction<typeof useTranslation>

describe('LanguageToggle', () => {
  const mockChangeLocale = jest.fn()

  beforeEach(() => {
    mockChangeLocale.mockClear()
    mockUseTranslation.mockReturnValue({
      locale: 'ar',
      changeLocale: mockChangeLocale,
      t: (key: string) => key,
    })
  })

  it('renders the language toggle button', () => {
    render(<LanguageToggle />)
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('AR')
  })

  it('displays the current language flag and code', () => {
    render(<LanguageToggle />)
    
    // Check for Arabic flag emoji
    expect(screen.getByText('🇲🇦')).toBeInTheDocument()
    expect(screen.getByText('AR')).toBeInTheDocument()
  })

  it('opens dropdown menu when clicked', () => {
    render(<LanguageToggle />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)
    
    // Check if dropdown items are visible
    expect(screen.getByText('العربية')).toBeInTheDocument()
    expect(screen.getByText('Français')).toBeInTheDocument()
    expect(screen.getByText('English')).toBeInTheDocument()
  })

  it('calls changeLocale when a language is selected', () => {
    render(<LanguageToggle />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)
    
    const frenchOption = screen.getByText('Français')
    fireEvent.click(frenchOption)
    
    expect(mockChangeLocale).toHaveBeenCalledWith('fr')
  })

  it('highlights the current language in the dropdown', () => {
    render(<LanguageToggle />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)
    
    // The current language (Arabic) should have a check mark
    const arabicOption = screen.getByText('العربية').closest('[role="menuitem"]')
    expect(arabicOption).toHaveClass('bg-blue-100')
  })

  it('displays correct language when locale changes', () => {
    // Test with French locale
    mockUseTranslation.mockReturnValue({
      locale: 'fr',
      changeLocale: mockChangeLocale,
      t: (key: string) => key,
    })

    render(<LanguageToggle />)
    
    expect(screen.getByText('🇫🇷')).toBeInTheDocument()
    expect(screen.getByText('FR')).toBeInTheDocument()
  })

  it('displays correct language when locale is English', () => {
    // Test with English locale
    mockUseTranslation.mockReturnValue({
      locale: 'en',
      changeLocale: mockChangeLocale,
      t: (key: string) => key,
    })

    render(<LanguageToggle />)
    
    expect(screen.getByText('🇬🇧')).toBeInTheDocument()
    expect(screen.getByText('EN')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<LanguageToggle />)
    
    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-expanded', 'false')
    
    // Check for screen reader text
    expect(screen.getByText('تغيير اللغة / Change language')).toBeInTheDocument()
  })
})
