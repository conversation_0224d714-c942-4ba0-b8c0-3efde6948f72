"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  GraduationCap,
  ShoppingCart,
  Heart,
  Clock,
  Package,
  Star,
  Calendar,
  CreditCard,
  MapPin,
  Bell,
  Settings,
  Download,
  Share2,
  Plus,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

// أنواع البيانات
interface Order {
  id: string
  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered'
  items: OrderItem[]
  total: number
  created_at: string
  delivery_date?: string
  tracking_number?: string
}

interface OrderItem {
  id: string
  name: string
  image: string
  quantity: number
  price: number
  customizations?: any
}

interface SavedDesign {
  id: string
  name: string
  preview_image: string
  created_at: string
  customizations: any
}

export default function StudentDashboard() {
  const { user, profile } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [orders, setOrders] = useState<Order[]>([])
  const [savedDesigns, setSavedDesigns] = useState<SavedDesign[]>([])
  const [loading, setLoading] = useState(true)

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockOrders: Order[] = [
      {
        id: '1',
        status: 'in_production',
        items: [
          {
            id: '1',
            name: 'زي التخرج الكلاسيكي',
            image: '/api/placeholder/150/150',
            quantity: 1,
            price: 299.99
          }
        ],
        total: 299.99,
        created_at: '2024-01-15',
        delivery_date: '2024-02-01'
      },
      {
        id: '2',
        status: 'delivered',
        items: [
          {
            id: '2',
            name: 'قبعة التخرج المميزة',
            image: '/api/placeholder/150/150',
            quantity: 1,
            price: 89.99
          }
        ],
        total: 89.99,
        created_at: '2024-01-01',
        delivery_date: '2024-01-10'
      }
    ]

    const mockDesigns: SavedDesign[] = [
      {
        id: '1',
        name: 'تصميمي المفضل',
        preview_image: '/api/placeholder/200/200',
        created_at: '2024-01-20',
        customizations: {}
      },
      {
        id: '2',
        name: 'تصميم احتفال التخرج',
        preview_image: '/api/placeholder/200/200',
        created_at: '2024-01-18',
        customizations: {}
      }
    ]

    setOrders(mockOrders)
    setSavedDesigns(mockDesigns)
    setLoading(false)
  }, [])

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'in_production': return 'bg-purple-100 text-purple-800'
      case 'shipped': return 'bg-orange-100 text-orange-800'
      case 'delivered': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'في الانتظار'
      case 'confirmed': return 'مؤكد'
      case 'in_production': return 'قيد الإنتاج'
      case 'shipped': return 'تم الشحن'
      case 'delivered': return 'تم التسليم'
      default: return 'غير معروف'
    }
  }

  const getOrderProgress = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 20
      case 'confirmed': return 40
      case 'in_production': return 60
      case 'shipped': return 80
      case 'delivered': return 100
      default: return 0
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                مرحباً، {profile?.full_name || 'الطالب'}! 🎓
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة طلباتك وتصاميمك المخصصة
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                الإشعارات
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                الإعدادات
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي الطلبات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {orders.length}
                  </p>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    التصاميم المحفوظة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {savedDesigns.length}
                  </p>
                </div>
                <Heart className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    الطلبات النشطة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {orders.filter(o => o.status !== 'delivered').length}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    النقاط المكتسبة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    1,250
                  </p>
                </div>
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="arabic-text">
              <Package className="h-4 w-4 mr-2" />
              نظرة عامة
            </TabsTrigger>
            <TabsTrigger value="orders" className="arabic-text">
              <ShoppingCart className="h-4 w-4 mr-2" />
              طلباتي
            </TabsTrigger>
            <TabsTrigger value="designs" className="arabic-text">
              <Heart className="h-4 w-4 mr-2" />
              تصاميمي
            </TabsTrigger>
            <TabsTrigger value="profile" className="arabic-text">
              <Settings className="h-4 w-4 mr-2" />
              الملف الشخصي
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Recent Orders */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الطلبات الأخيرة</CardTitle>
                <CardDescription className="arabic-text">
                  آخر الطلبات وحالتها الحالية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orders.slice(0, 3).map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                          <Package className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <p className="font-medium arabic-text">طلب #{order.id}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {order.items.length} عنصر - {order.total} درهم
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(order.status)}>
                          {getStatusText(order.status)}
                        </Badge>
                        <div className="mt-2 w-32">
                          <Progress value={getOrderProgress(order.status)} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button variant="outline" className="w-full arabic-text">
                    عرض جميع الطلبات
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button className="h-20 flex-col gap-2" variant="outline">
                    <Plus className="h-6 w-6" />
                    <span className="arabic-text">طلب جديد</span>
                  </Button>
                  <Button className="h-20 flex-col gap-2" variant="outline">
                    <GraduationCap className="h-6 w-6" />
                    <span className="arabic-text">تخصيص زي</span>
                  </Button>
                  <Button className="h-20 flex-col gap-2" variant="outline">
                    <MapPin className="h-6 w-6" />
                    <span className="arabic-text">تتبع الطلب</span>
                  </Button>
                  <Button className="h-20 flex-col gap-2" variant="outline">
                    <CreditCard className="h-6 w-6" />
                    <span className="arabic-text">الفواتير</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-6 mt-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold arabic-text">طلباتي</h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                <span className="arabic-text">طلب جديد</span>
              </Button>
            </div>

            <div className="grid gap-6">
              {orders.map((order) => (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="arabic-text">طلب #{order.id}</CardTitle>
                        <CardDescription className="arabic-text">
                          تاريخ الطلب: {new Date(order.created_at).toLocaleDateString('ar-SA')}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(order.status)}>
                        {getStatusText(order.status)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Order Items */}
                      <div className="space-y-3">
                        {order.items.map((item) => (
                          <div key={item.id} className="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                            <div className="flex-1">
                              <p className="font-medium arabic-text">{item.name}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                                الكمية: {item.quantity} × {item.price} درهم
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Order Progress */}
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium arabic-text">تقدم الطلب</span>
                          <span className="text-sm text-gray-600">{getOrderProgress(order.status)}%</span>
                        </div>
                        <Progress value={getOrderProgress(order.status)} className="h-2" />
                      </div>

                      {/* Order Total */}
                      <div className="flex justify-between items-center pt-3 border-t">
                        <span className="font-medium arabic-text">الإجمالي:</span>
                        <span className="text-lg font-bold text-blue-600">{order.total} درهم</span>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 pt-3">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          <span className="arabic-text">عرض التفاصيل</span>
                        </Button>
                        {order.status === 'shipped' && (
                          <Button variant="outline" size="sm">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span className="arabic-text">تتبع الشحنة</span>
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          <span className="arabic-text">تحميل الفاتورة</span>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Designs Tab */}
          <TabsContent value="designs" className="space-y-6 mt-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold arabic-text">تصاميمي المحفوظة</h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                <span className="arabic-text">تصميم جديد</span>
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {savedDesigns.map((design) => (
                <Card key={design.id}>
                  <CardContent className="p-0">
                    <div className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-t-lg"></div>
                    <div className="p-4">
                      <h3 className="font-medium arabic-text mb-2">{design.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text mb-4">
                        تم الحفظ: {new Date(design.created_at).toLocaleDateString('ar-SA')}
                      </p>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Eye className="h-4 w-4 mr-2" />
                          <span className="arabic-text">عرض</span>
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">
                          <Edit className="h-4 w-4 mr-2" />
                          <span className="arabic-text">تعديل</span>
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الملف الشخصي</CardTitle>
                <CardDescription className="arabic-text">
                  إدارة معلوماتك الشخصية وإعدادات الحساب
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium arabic-text">الاسم الكامل</label>
                      <p className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text">
                        {profile?.full_name || 'غير محدد'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium arabic-text">البريد الإلكتروني</label>
                      <p className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                        {profile?.email || 'غير محدد'}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium arabic-text">رقم الهاتف</label>
                      <p className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text">
                        {profile?.phone || 'غير محدد'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium arabic-text">اسم المدرسة</label>
                      <p className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text">
                        {profile?.school_name || 'غير محدد'}
                      </p>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button>
                      <Edit className="h-4 w-4 mr-2" />
                      <span className="arabic-text">تعديل الملف الشخصي</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
