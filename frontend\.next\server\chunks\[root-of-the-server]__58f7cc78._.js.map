{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport function createClient(cookieStore: ReturnType<typeof cookies>) {\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          try {\n            cookieStore.set({ name, value, ...options })\n          } catch (error) {\n            // The `set` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n        remove(name: string, options: any) {\n          try {\n            cookieStore.set({ name, value: '', ...options })\n          } catch (error) {\n            // The `delete` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS,aAAa,WAAuC;IAClE,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM;wBAAO,GAAG,OAAO;oBAAC;gBAC5C,EAAE,OAAO,OAAO;gBACd,uDAAuD;gBACvD,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM,OAAO;wBAAI,GAAG,OAAO;oBAAC;gBAChD,EAAE,OAAO,OAAO;gBACd,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/pages/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { cookies } from 'next/headers'\nimport { createClient } from '@/lib/supabase/server'\n\n// GET - جلب جميع الصفحات\nexport async function GET(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    const { searchParams } = new URL(request.url)\n    const includeUnpublished = searchParams.get('include_unpublished') === 'true'\n    const language = searchParams.get('language') || 'ar'\n\n    // التحقق من صلاحيات الأدمن للصفحات غير المنشورة\n    let canViewUnpublished = false\n    if (includeUnpublished) {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (user) {\n        const { data: profile } = await supabase\n          .from('profiles')\n          .select('role')\n          .eq('id', user.id)\n          .single()\n        \n        canViewUnpublished = profile?.role === 'admin'\n      }\n    }\n\n    let query = supabase\n      .from('pages')\n      .select(`\n        *,\n        page_content!inner(\n          title,\n          meta_description,\n          language\n        ),\n        profiles(full_name)\n      `)\n      .eq('page_content.language', language)\n      .order('created_at', { ascending: false })\n\n    // تطبيق فلتر النشر\n    if (!canViewUnpublished) {\n      query = query.eq('is_published', true)\n    }\n\n    const { data: pages, error } = await query\n\n    if (error) {\n      console.error('Error fetching pages:', error)\n      return NextResponse.json(\n        { error: 'فشل في جلب الصفحات' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ pages })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة صفحة جديدة\nexport async function POST(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات الأدمن\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بهذا الإجراء' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    const {\n      slug,\n      is_published,\n      featured_image,\n      content // { ar: {title, content, meta_description}, en: {...}, fr: {...} }\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!slug || !content || !content.ar || !content.ar.title || !content.ar.content) {\n      return NextResponse.json(\n        { error: 'البيانات المطلوبة مفقودة' },\n        { status: 400 }\n      )\n    }\n\n    // التحقق من عدم تكرار الـ slug\n    const { data: existingPage } = await supabase\n      .from('pages')\n      .select('id')\n      .eq('slug', slug)\n      .single()\n\n    if (existingPage) {\n      return NextResponse.json(\n        { error: 'الرابط المختصر موجود بالفعل' },\n        { status: 400 }\n      )\n    }\n\n    // إدراج الصفحة في قاعدة البيانات\n    const { data: page, error: insertError } = await supabase\n      .from('pages')\n      .insert({\n        slug,\n        is_published: is_published ?? false,\n        featured_image: featured_image || null,\n        author_id: user.id\n      })\n      .select()\n      .single()\n\n    if (insertError) {\n      console.error('Error inserting page:', insertError)\n      return NextResponse.json(\n        { error: 'فشل في إضافة الصفحة' },\n        { status: 500 }\n      )\n    }\n\n    // إدراج محتوى الصفحة بجميع اللغات\n    const contentInserts = []\n    for (const [language, langContent] of Object.entries(content)) {\n      if (langContent && typeof langContent === 'object' && langContent.title && langContent.content) {\n        contentInserts.push({\n          page_id: page.id,\n          language: language as 'ar' | 'en' | 'fr',\n          title: langContent.title,\n          content: langContent.content,\n          meta_description: langContent.meta_description || null,\n          meta_keywords: langContent.meta_keywords || null\n        })\n      }\n    }\n\n    if (contentInserts.length > 0) {\n      const { error: contentError } = await supabase\n        .from('page_content')\n        .insert(contentInserts)\n\n      if (contentError) {\n        // حذف الصفحة في حالة فشل إدراج المحتوى\n        await supabase.from('pages').delete().eq('id', page.id)\n        console.error('Error inserting page content:', contentError)\n        return NextResponse.json(\n          { error: 'فشل في إضافة محتوى الصفحة' },\n          { status: 500 }\n        )\n      }\n    }\n\n    return NextResponse.json({ \n      message: 'تم إضافة الصفحة بنجاح',\n      page \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,qBAAqB,aAAa,GAAG,CAAC,2BAA2B;QACvE,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QAEjD,gDAAgD;QAChD,IAAI,qBAAqB;QACzB,IAAI,oBAAoB;YACtB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,MAAM;gBACR,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;gBAET,qBAAqB,SAAS,SAAS;YACzC;QACF;QAEA,IAAI,QAAQ,SACT,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,yBAAyB,UAC5B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,mBAAmB;QACnB,IAAI,CAAC,oBAAoB;YACvB,QAAQ,MAAM,EAAE,CAAC,gBAAgB;QACnC;QAEA,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAErC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,2BAA2B;QAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,cAAc,EACd,OAAO,AAAC,mEAAmE;UAC5E,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE;YAChF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,QAAQ,MACX,MAAM;QAET,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAC9C,IAAI,CAAC,SACL,MAAM,CAAC;YACN;YACA,cAAc,gBAAgB;YAC9B,gBAAgB,kBAAkB;YAClC,WAAW,KAAK,EAAE;QACpB,GACC,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,MAAM,iBAAiB,EAAE;QACzB,KAAK,MAAM,CAAC,UAAU,YAAY,IAAI,OAAO,OAAO,CAAC,SAAU;YAC7D,IAAI,eAAe,OAAO,gBAAgB,YAAY,YAAY,KAAK,IAAI,YAAY,OAAO,EAAE;gBAC9F,eAAe,IAAI,CAAC;oBAClB,SAAS,KAAK,EAAE;oBAChB,UAAU;oBACV,OAAO,YAAY,KAAK;oBACxB,SAAS,YAAY,OAAO;oBAC5B,kBAAkB,YAAY,gBAAgB,IAAI;oBAClD,eAAe,YAAY,aAAa,IAAI;gBAC9C;YACF;QACF;QAEA,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,gBACL,MAAM,CAAC;YAEV,IAAI,cAAc;gBAChB,uCAAuC;gBACvC,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM,KAAK,EAAE;gBACtD,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA4B,GACrC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}