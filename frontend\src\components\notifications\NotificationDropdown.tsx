"use client"

import { useState } from 'react'
import { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Bell,
  Check,
  CheckCheck,
  Trash2,
  ExternalLink,
  Settings,
  X
} from 'lucide-react'

export function NotificationDropdown() {
  const { 
    notifications, 
    unreadCount, 
    markAsRead, 
    markAllAsRead, 
    removeNotification, 
    clearAll 
  } = useNotifications()
  
  const [isOpen, setIsOpen] = useState(false)

  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {
    markAsRead(notificationId)
    if (actionUrl) {
      window.location.href = actionUrl
    }
  }

  const recentNotifications = notifications.slice(0, 10)

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end" 
        className="w-80 p-0"
        sideOffset={5}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold arabic-text">الإشعارات</h3>
          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="h-8 px-2 text-xs arabic-text"
              >
                <CheckCheck className="h-3 w-3 mr-1" />
                قراءة الكل
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Notifications List */}
        <ScrollArea className="h-96">
          {recentNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Bell className="h-12 w-12 text-gray-400 mb-3" />
              <p className="text-gray-500 arabic-text">لا توجد إشعارات</p>
              <p className="text-sm text-gray-400 arabic-text">ستظهر إشعاراتك هنا</p>
            </div>
          ) : (
            <div className="divide-y">
              {recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${
                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}
                >
                  <div className="flex items-start gap-3">
                    {/* Icon */}
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                      getNotificationColor(notification.priority)
                    }`}>
                      {getNotificationIcon(notification.type)}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h4 className={`text-sm font-medium arabic-text ${
                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {notification.title}
                        </h4>
                        
                        {/* Actions */}
                        <div className="flex items-center gap-1 ml-2">
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeNotification(notification.id)
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2">
                        {notification.message}
                      </p>

                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500">
                          {formatNotificationTime(notification.createdAt)}
                        </span>

                        {notification.actionText && notification.actionUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleNotificationClick(notification.id, notification.actionUrl)
                            }}
                          >
                            {notification.actionText}
                            <ExternalLink className="h-3 w-3 mr-1" />
                          </Button>
                        )}
                      </div>

                      {/* Expiry Warning */}
                      {notification.expiresAt && (
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs arabic-text">
                            ينتهي في {formatNotificationTime(notification.expiresAt)}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {/* Footer */}
        {notifications.length > 0 && (
          <>
            <Separator />
            <div className="p-3 flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs arabic-text"
                asChild
              >
                <a href="/notifications">عرض جميع الإشعارات</a>
              </Button>
              
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAll}
                  className="text-xs text-red-600 hover:text-red-700 arabic-text"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  حذف الكل
                </Button>
              )}
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// مكون إشعار منبثق للإشعارات الهامة
interface ToastNotificationProps {
  notification: {
    id: string
    title: string
    message: string
    type: string
    priority: string
  }
  onClose: () => void
  onAction?: () => void
}

export function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {
  return (
    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${
      getNotificationColor(notification.priority as any)
    } animate-in slide-in-from-right duration-300`}>
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 text-lg">
          {getNotificationIcon(notification.type as any)}
        </div>
        
        <div className="flex-1">
          <h4 className="font-medium arabic-text">{notification.title}</h4>
          <p className="text-sm mt-1 arabic-text">{notification.message}</p>
          
          <div className="flex items-center gap-2 mt-3">
            {onAction && (
              <Button size="sm" onClick={onAction} className="arabic-text">
                عرض
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// مكون عداد الإشعارات البسيط
export function NotificationBadge() {
  const { unreadCount } = useNotifications()

  if (unreadCount === 0) return null

  return (
    <Badge 
      variant="destructive" 
      className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
    >
      {unreadCount > 99 ? '99+' : unreadCount}
    </Badge>
  )
}
