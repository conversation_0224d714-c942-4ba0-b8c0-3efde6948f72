// Global setup for Jest tests
module.exports = async () => {
  // Set timezone for consistent date testing
  process.env.TZ = 'UTC'
  
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
  
  // Suppress console warnings during tests
  const originalWarn = console.warn
  console.warn = (...args) => {
    // Suppress specific warnings that are expected in test environment
    const message = args[0]
    if (
      typeof message === 'string' &&
      (message.includes('Warning: ReactDOM.render is no longer supported') ||
       message.includes('Warning: componentWillReceiveProps') ||
       message.includes('Warning: componentWillUpdate') ||
       message.includes('act(...)'))
    ) {
      return
    }
    originalWarn.apply(console, args)
  }
  
  // Global test setup
  console.log('🧪 Setting up Jest test environment...')
  
  // Initialize any global test utilities
  global.testStartTime = Date.now()
}
