// Error monitoring and logging utilities
import React from 'react'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface ErrorInfo {
  id: string
  message: string
  stack?: string
  level: LogLevel
  timestamp: string
  url: string
  userAgent: string
  userId?: string
  sessionId: string
  metadata?: Record<string, any>
  context?: {
    component?: string
    action?: string
    props?: any
  }
}

export interface PerformanceIssue {
  id: string
  type: 'slow-render' | 'memory-leak' | 'large-bundle' | 'slow-api'
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  metrics: Record<string, number>
  suggestions: string[]
}

class ErrorMonitor {
  private errors: ErrorInfo[] = []
  private performanceIssues: PerformanceIssue[] = []
  private sessionId: string
  private maxErrors = 100
  private isEnabled = true

  constructor() {
    this.sessionId = this.generateSessionId()
    this.initializeErrorHandlers()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeErrorHandlers(): void {
    if (typeof window === 'undefined') return

    // Global error handler
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        level: LogLevel.ERROR,
        context: {
          component: 'Global',
          action: 'Runtime Error'
        },
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      })
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        level: LogLevel.ERROR,
        context: {
          component: 'Global',
          action: 'Promise Rejection'
        },
        metadata: {
          reason: event.reason
        }
      })
    })

    // Console error override
    const originalConsoleError = console.error
    console.error = (...args) => {
      this.logError({
        message: args.join(' '),
        level: LogLevel.ERROR,
        context: {
          component: 'Console',
          action: 'Console Error'
        }
      })
      originalConsoleError.apply(console, args)
    }
  }

  // Log an error
  logError(errorData: Partial<ErrorInfo>): void {
    if (!this.isEnabled) return

    const error: ErrorInfo = {
      id: this.generateErrorId(),
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      level: errorData.level || LogLevel.ERROR,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      sessionId: this.sessionId,
      metadata: errorData.metadata,
      context: errorData.context,
      ...errorData
    }

    this.errors.push(error)

    // Keep only the latest errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors)
    }

    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalService(error)
    } else {
      // Log to console in development
      this.logToConsole(error)
    }
  }

  // Log performance issue
  logPerformanceIssue(issue: Omit<PerformanceIssue, 'id' | 'timestamp'>): void {
    const performanceIssue: PerformanceIssue = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      ...issue
    }

    this.performanceIssues.push(performanceIssue)

    if (process.env.NODE_ENV === 'development') {
      console.warn('🐌 Performance Issue:', performanceIssue)
    }
  }

  // Log different levels
  debug(message: string, metadata?: Record<string, any>, context?: ErrorInfo['context']): void {
    this.logError({ message, level: LogLevel.DEBUG, metadata, context })
  }

  info(message: string, metadata?: Record<string, any>, context?: ErrorInfo['context']): void {
    this.logError({ message, level: LogLevel.INFO, metadata, context })
  }

  warn(message: string, metadata?: Record<string, any>, context?: ErrorInfo['context']): void {
    this.logError({ message, level: LogLevel.WARN, metadata, context })
  }

  error(message: string, error?: Error, metadata?: Record<string, any>, context?: ErrorInfo['context']): void {
    this.logError({
      message,
      stack: error?.stack,
      level: LogLevel.ERROR,
      metadata: {
        ...metadata,
        errorName: error?.name,
        errorMessage: error?.message
      },
      context
    })
  }

  fatal(message: string, error?: Error, metadata?: Record<string, any>, context?: ErrorInfo['context']): void {
    this.logError({
      message,
      stack: error?.stack,
      level: LogLevel.FATAL,
      metadata: {
        ...metadata,
        errorName: error?.name,
        errorMessage: error?.message
      },
      context
    })
  }

  // Get all errors
  getErrors(): ErrorInfo[] {
    return [...this.errors]
  }

  // Get errors by level
  getErrorsByLevel(level: LogLevel): ErrorInfo[] {
    return this.errors.filter(error => error.level === level)
  }

  // Get performance issues
  getPerformanceIssues(): PerformanceIssue[] {
    return [...this.performanceIssues]
  }

  // Clear all errors
  clearErrors(): void {
    this.errors = []
    this.performanceIssues = []
  }

  // Generate error report
  generateErrorReport(): {
    summary: {
      totalErrors: number
      errorsByLevel: Record<string, number>
      recentErrors: ErrorInfo[]
      criticalIssues: number
    }
    performanceIssues: PerformanceIssue[]
    recommendations: string[]
  } {
    const errorsByLevel = this.errors.reduce((acc, error) => {
      const levelName = LogLevel[error.level]
      acc[levelName] = (acc[levelName] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recentErrors = this.errors.slice(-10)
    const criticalIssues = this.errors.filter(e => e.level >= LogLevel.ERROR).length

    const recommendations = this.generateRecommendations()

    return {
      summary: {
        totalErrors: this.errors.length,
        errorsByLevel,
        recentErrors,
        criticalIssues
      },
      performanceIssues: this.performanceIssues,
      recommendations
    }
  }

  // Set user context
  setUserContext(userId: string): void {
    this.errors.forEach(error => {
      if (!error.userId) {
        error.userId = userId
      }
    })
  }

  // Enable/disable monitoring
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private logToConsole(error: ErrorInfo): void {
    const levelColors = {
      [LogLevel.DEBUG]: 'color: #888',
      [LogLevel.INFO]: 'color: #2196F3',
      [LogLevel.WARN]: 'color: #FF9800',
      [LogLevel.ERROR]: 'color: #F44336',
      [LogLevel.FATAL]: 'color: #D32F2F; font-weight: bold'
    }

    const levelName = LogLevel[error.level]
    console.log(
      `%c[${levelName}] ${error.message}`,
      levelColors[error.level],
      error
    )
  }

  private async sendToExternalService(error: ErrorInfo): Promise<void> {
    try {
      // In a real application, you would send to services like:
      // - Sentry
      // - LogRocket
      // - Bugsnag
      // - Custom logging endpoint

      // Example implementation:
      /*
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error)
      })
      */

      // For now, just log to console in production
      console.error('Error logged:', error)
    } catch (sendError) {
      console.error('Failed to send error to external service:', sendError)
    }
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = []
    const errorCount = this.errors.length
    const criticalErrors = this.errors.filter(e => e.level >= LogLevel.ERROR).length
    const performanceIssueCount = this.performanceIssues.length

    if (criticalErrors > 10) {
      recommendations.push('عدد الأخطاء الحرجة مرتفع - راجع معالجة الأخطاء')
    }

    if (errorCount > 50) {
      recommendations.push('عدد الأخطاء الإجمالي مرتفع - فعل نظام مراقبة أفضل')
    }

    if (performanceIssueCount > 5) {
      recommendations.push('مشاكل الأداء متعددة - راجع تحسين الأداء')
    }

    const memoryLeaks = this.performanceIssues.filter(i => i.type === 'memory-leak')
    if (memoryLeaks.length > 0) {
      recommendations.push('تم اكتشاف تسريبات في الذاكرة - راجع إدارة الذاكرة')
    }

    const slowAPIs = this.performanceIssues.filter(i => i.type === 'slow-api')
    if (slowAPIs.length > 3) {
      recommendations.push('استدعاءات API بطيئة - راجع تحسين الشبكة')
    }

    if (recommendations.length === 0) {
      recommendations.push('الأداء جيد - استمر في المراقبة')
    }

    return recommendations
  }
}

// Global error monitor instance
export const errorMonitor = new ErrorMonitor()

// React error boundary hook
export function useErrorHandler() {
  return {
    logError: errorMonitor.logError.bind(errorMonitor),
    debug: errorMonitor.debug.bind(errorMonitor),
    info: errorMonitor.info.bind(errorMonitor),
    warn: errorMonitor.warn.bind(errorMonitor),
    error: errorMonitor.error.bind(errorMonitor),
    fatal: errorMonitor.fatal.bind(errorMonitor),
    logPerformanceIssue: errorMonitor.logPerformanceIssue.bind(errorMonitor)
  }
}

// Error boundary component helper
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallbackComponent?: React.ComponentType<{ error: Error; resetError: () => void }>
) {
  return class ErrorBoundary extends React.Component<
    P,
    { hasError: boolean; error: Error | null }
  > {
    constructor(props: P) {
      super(props)
      this.state = { hasError: false, error: null }
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: any) {
      errorMonitor.error(
        `React Error Boundary: ${error.message}`,
        error,
        { errorInfo },
        {
          component: Component.name || 'Unknown',
          action: 'Component Error'
        }
      )
    }

    render() {
      if (this.state.hasError && this.state.error) {
        if (fallbackComponent) {
          const FallbackComponent = fallbackComponent
          return React.createElement(FallbackComponent, {
            error: this.state.error,
            resetError: () => this.setState({ hasError: false, error: null })
          })
        }

        return React.createElement('div', {
          className: 'error-boundary p-4 border border-red-300 rounded-lg bg-red-50'
        }, [
          React.createElement('h2', {
            key: 'title',
            className: 'text-red-800 font-bold mb-2'
          }, 'حدث خطأ غير متوقع'),
          React.createElement('p', {
            key: 'message',
            className: 'text-red-600 text-sm'
          }, this.state.error.message),
          React.createElement('button', {
            key: 'retry',
            onClick: () => this.setState({ hasError: false, error: null }),
            className: 'mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700'
          }, 'إعادة المحاولة')
        ])
      }

      return React.createElement(Component, this.props as P)
    }
  }
}

// Utility functions
export const captureException = (error: Error, context?: ErrorInfo['context']) => {
  errorMonitor.error(error.message, error, undefined, context)
}

export const captureMessage = (message: string, level: LogLevel = LogLevel.INFO) => {
  errorMonitor.logError({ message, level })
}

// Performance monitoring integration
export const monitorComponentPerformance = (componentName: string) => {
  return {
    onSlowRender: (renderTime: number) => {
      if (renderTime > 16) { // More than one frame (16ms)
        errorMonitor.logPerformanceIssue({
          type: 'slow-render',
          description: `Component ${componentName} rendered slowly`,
          severity: renderTime > 100 ? 'high' : 'medium',
          metrics: { renderTime },
          suggestions: [
            'Consider memoizing the component',
            'Check for unnecessary re-renders',
            'Optimize heavy computations'
          ]
        })
      }
    }
  }
}
