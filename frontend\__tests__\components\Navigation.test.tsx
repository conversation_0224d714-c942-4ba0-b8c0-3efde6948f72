import { render, screen, fireEvent } from '@testing-library/react'
import { Navigation } from '@/components/Navigation'
import { AuthProvider } from '@/contexts/AuthContext'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { ThemeProvider } from '@/components/theme-provider'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  usePathname: () => '/',
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}))

// Mock useTranslation hook
jest.mock('@/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    language: 'ar',
    setLanguage: jest.fn(),
  }),
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider attribute="class" defaultTheme="light">
    <AuthProvider>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </AuthProvider>
  </ThemeProvider>
)

describe('Navigation Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
  })

  test('renders navigation with logo', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check if logo is present
    const logo = screen.getByText('Graduation Toqs')
    expect(logo).toBeInTheDocument()
  })

  test('renders main navigation items', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for main navigation items
    expect(screen.getByText('navigation.home')).toBeInTheDocument()
    expect(screen.getByText('navigation.catalog')).toBeInTheDocument()
    expect(screen.getByText('navigation.customize')).toBeInTheDocument()
    expect(screen.getByText('تتبع الطلب')).toBeInTheDocument()
  })

  test('shows mobile menu when hamburger is clicked', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Find and click the mobile menu button
    const mobileMenuButton = screen.getByRole('button', { name: /menu/i })
    fireEvent.click(mobileMenuButton)

    // Check if mobile menu items are visible
    // Note: This test might need adjustment based on actual mobile menu implementation
    expect(screen.getAllByText('navigation.home')).toHaveLength(2) // Desktop + Mobile
  })

  test('displays cart icon with item count', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for cart icon and count
    const cartLink = screen.getByRole('link', { name: /cart/i })
    expect(cartLink).toBeInTheDocument()
    expect(cartLink).toHaveAttribute('href', '/cart')
    
    // Check for cart count badge
    const cartCount = screen.getByText('2')
    expect(cartCount).toBeInTheDocument()
  })

  test('shows notification dropdown', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for notification button
    const notificationButton = screen.getByRole('button')
    expect(notificationButton).toBeInTheDocument()
  })

  test('renders theme toggle button', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for theme toggle (sun/moon icon)
    const themeToggle = screen.getByRole('button', { name: /toggle theme/i })
    expect(themeToggle).toBeInTheDocument()
  })

  test('renders language toggle', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for language toggle
    const languageToggle = screen.getByRole('button', { name: /language/i })
    expect(languageToggle).toBeInTheDocument()
  })

  test('navigation links have correct href attributes', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check navigation links
    const homeLink = screen.getByRole('link', { name: /navigation.home/i })
    expect(homeLink).toHaveAttribute('href', '/')

    const catalogLink = screen.getByRole('link', { name: /navigation.catalog/i })
    expect(catalogLink).toHaveAttribute('href', '/catalog')

    const customizeLink = screen.getByRole('link', { name: /navigation.customize/i })
    expect(customizeLink).toHaveAttribute('href', '/customize')
  })

  test('applies active state to current page', () => {
    // Mock usePathname to return '/catalog'
    jest.doMock('next/navigation', () => ({
      usePathname: () => '/catalog',
      useRouter: () => ({
        push: jest.fn(),
        replace: jest.fn(),
        back: jest.fn(),
      }),
    }))

    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check if catalog link has active styling
    const catalogLink = screen.getByRole('link', { name: /navigation.catalog/i })
    expect(catalogLink).toHaveClass('text-blue-600')
  })

  test('handles responsive design classes', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for responsive classes
    const nav = screen.getByRole('navigation')
    expect(nav).toHaveClass('sticky', 'top-0', 'z-50')

    // Check for desktop actions container
    const desktopActions = screen.getByText('navigation.home').closest('.hidden.md\\:flex')
    expect(desktopActions).toBeInTheDocument()
  })
})

// Integration tests
describe('Navigation Integration', () => {
  test('navigation works with authentication context', () => {
    // Mock authenticated user
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      role: 'student'
    }

    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Test should pass with proper auth context
    expect(screen.getByText('Graduation Toqs')).toBeInTheDocument()
  })

  test('navigation works with notification context', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check notification integration
    const notificationButton = screen.getByRole('button')
    expect(notificationButton).toBeInTheDocument()
  })
})

// Accessibility tests
describe('Navigation Accessibility', () => {
  test('has proper ARIA labels', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for proper navigation role
    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()

    // Check for button accessibility
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toBeInTheDocument()
    })
  })

  test('supports keyboard navigation', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check if links are focusable
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAttribute('href')
    })

    // Check if buttons are focusable
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).not.toHaveAttribute('disabled')
    })
  })

  test('has proper contrast and visibility', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Check for proper text contrast classes
    const logo = screen.getByText('Graduation Toqs')
    expect(logo).toHaveClass('text-blue-600')
  })
})

// Performance tests
describe('Navigation Performance', () => {
  test('renders without performance issues', () => {
    const startTime = performance.now()
    
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Should render within reasonable time (less than 100ms)
    expect(renderTime).toBeLessThan(100)
  })

  test('handles multiple re-renders efficiently', () => {
    const { rerender } = render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    )

    // Re-render multiple times
    for (let i = 0; i < 10; i++) {
      rerender(
        <TestWrapper>
          <Navigation />
        </TestWrapper>
      )
    }

    // Should still be responsive
    expect(screen.getByText('Graduation Toqs')).toBeInTheDocument()
  })
})
