"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Upload,
  Image,
  Video,
  FileText,
  Music,
  Archive,
  Search,
  Filter,
  Grid3X3,
  List,
  Download,
  Trash2,
  Edit,
  Eye,
  Share2,
  FolderPlus,
  Folder,
  MoreVertical,
  Calendar,
  HardDrive
} from 'lucide-react'

// أنواع البيانات
interface MediaFile {
  id: string
  name: string
  type: 'image' | 'video' | 'document' | 'audio' | 'archive'
  size: number
  url: string
  thumbnail?: string
  uploadedAt: string
  uploadedBy: string
  folder?: string
  tags: string[]
  description?: string
}

interface MediaFolder {
  id: string
  name: string
  fileCount: number
  createdAt: string
  color: string
}

interface MediaLibraryProps {
  onSelectFile?: (file: MediaFile) => void
  allowMultiple?: boolean
  fileTypes?: string[]
  maxSize?: number
}

export function MediaLibrary({
  onSelectFile,
  allowMultiple = false,
  fileTypes = ['image', 'video', 'document'],
  maxSize = 10 * 1024 * 1024 // 10MB
}: MediaLibraryProps) {
  const [files, setFiles] = useState<MediaFile[]>([])
  const [folders, setFolders] = useState<MediaFolder[]>([])
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [currentFolder, setCurrentFolder] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  // تحميل البيانات الوهمية
  useEffect(() => {
    const mockFiles: MediaFile[] = [
      {
        id: '1',
        name: 'graduation-gown-classic.jpg',
        type: 'image',
        size: 2.5 * 1024 * 1024,
        url: '/api/placeholder/400/300',
        thumbnail: '/api/placeholder/150/150',
        uploadedAt: '2024-01-20T10:00:00Z',
        uploadedBy: 'أحمد محمد',
        folder: 'products',
        tags: ['زي تخرج', 'كلاسيكي', 'أسود'],
        description: 'صورة زي التخرج الكلاسيكي'
      },
      {
        id: '2',
        name: 'graduation-cap-premium.jpg',
        type: 'image',
        size: 1.8 * 1024 * 1024,
        url: '/api/placeholder/400/300',
        thumbnail: '/api/placeholder/150/150',
        uploadedAt: '2024-01-19T14:30:00Z',
        uploadedBy: 'سارة أحمد',
        folder: 'products',
        tags: ['قبعة', 'مميز', 'ذهبي'],
        description: 'قبعة التخرج المميزة'
      },
      {
        id: '3',
        name: 'graduation-ceremony-video.mp4',
        type: 'video',
        size: 45 * 1024 * 1024,
        url: '/api/placeholder/video',
        thumbnail: '/api/placeholder/150/150',
        uploadedAt: '2024-01-18T16:45:00Z',
        uploadedBy: 'محمد حسن',
        folder: 'marketing',
        tags: ['فيديو', 'حفل تخرج', 'ترويجي'],
        description: 'فيديو ترويجي لحفل التخرج'
      },
      {
        id: '4',
        name: 'size-guide.pdf',
        type: 'document',
        size: 0.5 * 1024 * 1024,
        url: '/api/placeholder/document',
        uploadedAt: '2024-01-17T09:15:00Z',
        uploadedBy: 'فاطمة علي',
        folder: 'guides',
        tags: ['دليل', 'مقاسات', 'PDF'],
        description: 'دليل المقاسات'
      }
    ]

    const mockFolders: MediaFolder[] = [
      { id: '1', name: 'المنتجات', fileCount: 25, createdAt: '2024-01-01T00:00:00Z', color: 'bg-blue-100 text-blue-800' },
      { id: '2', name: 'التسويق', fileCount: 12, createdAt: '2024-01-05T00:00:00Z', color: 'bg-green-100 text-green-800' },
      { id: '3', name: 'الأدلة', fileCount: 8, createdAt: '2024-01-10T00:00:00Z', color: 'bg-purple-100 text-purple-800' },
      { id: '4', name: 'المدونة', fileCount: 15, createdAt: '2024-01-12T00:00:00Z', color: 'bg-orange-100 text-orange-800' }
    ]

    setFiles(mockFiles)
    setFolders(mockFolders)
  }, [])

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesType = filterType === null || file.type === filterType
    const matchesFolder = currentFolder === null || file.folder === currentFolder
    
    return matchesSearch && matchesType && matchesFolder
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image className="h-5 w-5" />
      case 'video': return <Video className="h-5 w-5" />
      case 'document': return <FileText className="h-5 w-5" />
      case 'audio': return <Music className="h-5 w-5" />
      case 'archive': return <Archive className="h-5 w-5" />
      default: return <FileText className="h-5 w-5" />
    }
  }

  const handleFileSelect = (file: MediaFile) => {
    if (allowMultiple) {
      setSelectedFiles(prev => 
        prev.includes(file.id) 
          ? prev.filter(id => id !== file.id)
          : [...prev, file.id]
      )
    } else {
      setSelectedFiles([file.id])
      onSelectFile?.(file)
    }
  }

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    setUploading(true)
    setUploadProgress(0)

    // محاكاة رفع الملفات
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      // محاكاة التقدم
      for (let progress = 0; progress <= 100; progress += 10) {
        setUploadProgress(progress)
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // إضافة الملف للقائمة
      const newFile: MediaFile = {
        id: Date.now().toString() + i,
        name: file.name,
        type: file.type.startsWith('image/') ? 'image' : 
              file.type.startsWith('video/') ? 'video' : 'document',
        size: file.size,
        url: URL.createObjectURL(file),
        uploadedAt: new Date().toISOString(),
        uploadedBy: 'المستخدم الحالي',
        tags: [],
        folder: currentFolder || undefined
      }
      
      setFiles(prev => [newFile, ...prev])
    }

    setUploading(false)
    setUploadProgress(0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white arabic-text">
            مكتبة الوسائط 📁
          </h2>
          <p className="text-gray-600 dark:text-gray-300 arabic-text">
            إدارة الصور والفيديوهات والملفات
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
            {uploading ? (
              <div className="space-y-4">
                <Upload className="h-12 w-12 text-blue-600 mx-auto animate-bounce" />
                <div>
                  <p className="text-lg font-medium arabic-text">جاري رفع الملفات...</p>
                  <Progress value={uploadProgress} className="mt-2" />
                  <p className="text-sm text-gray-500 mt-1">{uploadProgress}%</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                <div>
                  <p className="text-lg font-medium arabic-text">اسحب الملفات هنا أو</p>
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      multiple
                      className="hidden"
                      onChange={handleUpload}
                      accept={fileTypes.map(type => {
                        switch (type) {
                          case 'image': return 'image/*'
                          case 'video': return 'video/*'
                          case 'document': return '.pdf,.doc,.docx'
                          default: return '*/*'
                        }
                      }).join(',')}
                    />
                    <Button className="arabic-text">
                      اختر الملفات
                    </Button>
                  </label>
                </div>
                <p className="text-sm text-gray-500 arabic-text">
                  الحد الأقصى: {formatFileSize(maxSize)} لكل ملف
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في الملفات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 arabic-text"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant={filterType === null ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterType(null)}
                className="arabic-text"
              >
                الكل
              </Button>
              <Button
                variant={filterType === 'image' ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterType('image')}
              >
                <Image className="h-4 w-4 mr-1" />
                صور
              </Button>
              <Button
                variant={filterType === 'video' ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterType('video')}
              >
                <Video className="h-4 w-4 mr-1" />
                فيديو
              </Button>
              <Button
                variant={filterType === 'document' ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterType('document')}
              >
                <FileText className="h-4 w-4 mr-1" />
                مستندات
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Folders */}
      {currentFolder === null && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 arabic-text">
            المجلدات 📂
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {folders.map((folder) => (
              <Card 
                key={folder.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setCurrentFolder(folder.id)}
              >
                <CardContent className="p-4 text-center">
                  <Folder className="h-12 w-12 text-blue-600 mx-auto mb-2" />
                  <h4 className="font-medium arabic-text">{folder.name}</h4>
                  <p className="text-sm text-gray-500 arabic-text">
                    {folder.fileCount} ملف
                  </p>
                </CardContent>
              </Card>
            ))}
            
            <Card className="cursor-pointer hover:shadow-md transition-shadow border-dashed">
              <CardContent className="p-4 text-center">
                <FolderPlus className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500 arabic-text">مجلد جديد</p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Breadcrumb */}
      {currentFolder && (
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <button 
            onClick={() => setCurrentFolder(null)}
            className="hover:text-blue-600 arabic-text"
          >
            الرئيسية
          </button>
          <span>/</span>
          <span className="arabic-text">
            {folders.find(f => f.id === currentFolder)?.name}
          </span>
        </div>
      )}

      {/* Files */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white arabic-text">
            الملفات ({filteredFiles.length})
          </h3>
          
          {selectedFiles.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                {selectedFiles.length} محدد
              </span>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                تحميل
              </Button>
              <Button variant="outline" size="sm">
                <Trash2 className="h-4 w-4 mr-1" />
                حذف
              </Button>
            </div>
          )}
        </div>

        {viewMode === 'grid' ? (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {filteredFiles.map((file) => (
              <Card 
                key={file.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedFiles.includes(file.id) ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => handleFileSelect(file)}
              >
                <CardContent className="p-3">
                  <div className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg mb-2 flex items-center justify-center">
                    {file.type === 'image' ? (
                      <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"></div>
                    ) : (
                      <div className="text-gray-400">
                        {getFileIcon(file.type)}
                      </div>
                    )}
                  </div>
                  
                  <h4 className="font-medium text-sm truncate arabic-text">
                    {file.name}
                  </h4>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </p>
                  
                  <div className="flex gap-1 mt-2">
                    {file.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {filteredFiles.map((file) => (
              <Card 
                key={file.id}
                className={`cursor-pointer transition-all hover:shadow-sm ${
                  selectedFiles.includes(file.id) ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => handleFileSelect(file)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center flex-shrink-0">
                      {file.type === 'image' ? (
                        <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"></div>
                      ) : (
                        <div className="text-gray-400">
                          {getFileIcon(file.type)}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-medium arabic-text">{file.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                        <span>{formatFileSize(file.size)}</span>
                        <span className="arabic-text">بواسطة {file.uploadedBy}</span>
                        <span>{new Date(file.uploadedAt).toLocaleDateString('ar-SA')}</span>
                      </div>
                      
                      <div className="flex gap-1 mt-2">
                        {file.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {filteredFiles.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <HardDrive className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text">
                لا توجد ملفات
              </h3>
              <p className="text-gray-600 dark:text-gray-400 arabic-text">
                {searchTerm || filterType 
                  ? 'لم يتم العثور على ملفات تطابق البحث' 
                  : 'ابدأ برفع ملفاتك الأولى'
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
