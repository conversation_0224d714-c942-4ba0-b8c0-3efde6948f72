"use client"

import { useTranslation } from '@/hooks/useTranslation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Navigation } from '@/components/Navigation'
import { 
  GraduationCap, 
  Target,
  Eye,
  Heart,
  Users,
  Award,
  Globe,
  Sparkles
} from 'lucide-react'

export default function AboutPage() {
  const { t } = useTranslation()

  const values = [
    {
      icon: <Heart className="h-8 w-8 text-red-500" />,
      title: 'الشغف بالتميز',
      description: 'نحن متحمسون لتقديم أفضل تجربة لطلابنا في يوم تخرجهم المميز'
    },
    {
      icon: <Award className="h-8 w-8 text-yellow-500" />,
      title: 'الجودة العالية',
      description: 'نستخدم أجود الخامات والتصاميم العصرية لضمان الأناقة والراحة'
    },
    {
      icon: <Users className="h-8 w-8 text-blue-500" />,
      title: 'خدمة العملاء',
      description: 'فريق متخصص لمساعدتك في كل خطوة من رحلة اختيار زي التخرج'
    },
    {
      icon: <Sparkles className="h-8 w-8 text-purple-500" />,
      title: 'الابتكار',
      description: 'نستخدم أحدث التقنيات والذكاء الاصطناعي لتحسين تجربتك'
    }
  ]

  const stats = [
    { number: '10,000+', label: 'طالب سعيد' },
    { number: '150+', label: 'مدرسة شريكة' },
    { number: '50+', label: 'مدينة مغربية' },
    { number: '99%', label: 'رضا العملاء' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      {/* Main Content */}
      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 dark:text-white mb-6 arabic-text">
            🎓 Graduation Toqs
          </h2>
          <p className="text-2xl text-blue-600 dark:text-blue-400 mb-4 arabic-text">
            أول منصة مغربية متخصصة في أزياء التخرج
          </p>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed arabic-text">
            نحن فريق شغوف بجعل يوم التخرج لا يُنسى لكل طالب مغربي. منصتنا تجمع بين التقنيات الحديثة 
            والتصاميم الأنيقة لتوفير تجربة استثنائية في اختيار وتخصيص أزياء التخرج.
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <Card className="text-center">
            <CardHeader>
              <Target className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <CardTitle className="text-2xl arabic-text">رسالتنا</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-lg leading-relaxed arabic-text">
                تمكين كل طالب مغربي من الحصول على زي تخرج مثالي يعكس شخصيته ويجعل يوم تخرجه 
                مميزاً وذا معنى، من خلال منصة رقمية سهلة الاستخدام وخدمات عالية الجودة.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Eye className="h-16 w-16 text-purple-600 mx-auto mb-4" />
              <CardTitle className="text-2xl arabic-text">رؤيتنا</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-lg leading-relaxed arabic-text">
                أن نصبح المنصة الرائدة في المغرب والمنطقة العربية لأزياء التخرج، ونساهم في 
                جعل كل احتفال تخرج تجربة فريدة ومميزة تبقى في الذاكرة إلى الأبد.
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12 arabic-text">
            قيمنا الأساسية
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto mb-4">
                    {value.icon}
                  </div>
                  <CardTitle className="arabic-text">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="arabic-text leading-relaxed">
                    {value.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Statistics */}
        <div className="bg-blue-600 dark:bg-blue-800 rounded-2xl p-8 mb-16">
          <h3 className="text-3xl font-bold text-center text-white mb-12 arabic-text">
            إنجازاتنا بالأرقام
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-blue-100 arabic-text">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Story */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-3xl text-center arabic-text">قصتنا</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-lg leading-relaxed arabic-text">
                بدأت فكرة Graduation Toqs من تجربة شخصية لأحد مؤسسي الشركة عندما واجه صعوبة في 
                العثور على زي تخرج مناسب وأنيق لحفل تخرجه. هذه التجربة ألهمتنا لإنشاء منصة تحل 
                هذه المشكلة لجميع الطلاب المغاربة.
              </p>
              
              <p className="text-lg leading-relaxed arabic-text">
                في عام 2024، أطلقنا منصتنا الرقمية الأولى في منطقة بني ملال-خنيفرة، وحققنا نجاحاً 
                كبيراً في خدمة المئات من الطلاب والمدارس. اليوم، نتطلع للتوسع في جميع أنحاء المغرب 
                ونسعى لتقديم خدماتنا لآلاف الطلاب سنوياً.
              </p>
              
              <p className="text-lg leading-relaxed arabic-text">
                نحن نؤمن بأن كل طالب يستحق أن يشعر بالفخر والأناقة في يوم تخرجه، ولهذا نعمل 
                باستمرار على تطوير منصتنا وتحسين خدماتنا لتلبية احتياجات عملائنا وتجاوز توقعاتهم.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Contact CTA */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            هل لديك أسئلة؟
          </h3>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 arabic-text">
            فريقنا مستعد لمساعدتك في أي وقت
          </p>
          <div className="flex justify-center gap-4">
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors arabic-text"
            >
              <Globe className="h-5 w-5" />
              تواصل معنا
            </a>
          </div>
        </div>
      </main>
    </div>
  )
}
