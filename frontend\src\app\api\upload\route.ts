import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

// POST - رفع الصور
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات المستخدم
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // التحقق من دور المستخدم
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || !['admin', 'school'].includes(profile.role)) {
      return NextResponse.json(
        { error: 'غير مصرح لك برفع الصور' },
        { status: 403 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const folder = formData.get('folder') as string || 'products'

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'لم يتم اختيار أي ملفات' },
        { status: 400 }
      )
    }

    const uploadedFiles: { name: string; url: string; path: string }[] = []
    const errors: string[] = []

    for (const file of files) {
      try {
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
          errors.push(`${file.name}: نوع الملف غير مدعوم`)
          continue
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
          errors.push(`${file.name}: حجم الملف كبير جداً (أكثر من 5 ميجابايت)`)
          continue
        }

        // إنشاء اسم فريد للملف
        const fileExtension = file.name.split('.').pop()
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`
        const filePath = `${folder}/${fileName}`

        // تحويل الملف إلى ArrayBuffer
        const arrayBuffer = await file.arrayBuffer()
        const fileBuffer = new Uint8Array(arrayBuffer)

        // رفع الملف إلى Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('images')
          .upload(filePath, fileBuffer, {
            contentType: file.type,
            cacheControl: '3600',
            upsert: false
          })

        if (uploadError) {
          console.error('Upload error:', uploadError)
          errors.push(`${file.name}: فشل في رفع الملف`)
          continue
        }

        // الحصول على رابط الملف العام
        const { data: urlData } = supabase.storage
          .from('images')
          .getPublicUrl(filePath)

        uploadedFiles.push({
          name: file.name,
          url: urlData.publicUrl,
          path: filePath
        })

      } catch (error) {
        console.error('Error processing file:', error)
        errors.push(`${file.name}: خطأ في معالجة الملف`)
      }
    }

    return NextResponse.json({
      message: `تم رفع ${uploadedFiles.length} من ${files.length} ملف بنجاح`,
      uploadedFiles,
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف صورة
export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // التحقق من صلاحيات المستخدم
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // التحقق من دور المستخدم
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || !['admin', 'school'].includes(profile.role)) {
      return NextResponse.json(
        { error: 'غير مصرح لك بحذف الصور' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const filePath = searchParams.get('path')

    if (!filePath) {
      return NextResponse.json(
        { error: 'مسار الملف مطلوب' },
        { status: 400 }
      )
    }

    // حذف الملف من Supabase Storage
    const { error: deleteError } = await supabase.storage
      .from('images')
      .remove([filePath])

    if (deleteError) {
      console.error('Delete error:', deleteError)
      return NextResponse.json(
        { error: 'فشل في حذف الملف' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'تم حذف الملف بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
