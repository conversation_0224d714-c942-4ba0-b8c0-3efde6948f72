import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

// GET - جلب صفحة بواسطة الـ slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'ar'

    // جلب الصفحة مع محتواها باللغة المحددة
    const { data: page, error } = await supabase
      .from('pages')
      .select(`
        *,
        page_content!inner(
          language,
          title,
          content,
          meta_description,
          meta_keywords
        ),
        profiles(full_name)
      `)
      .eq('slug', params.slug)
      .eq('page_content.language', language)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'الصفحة غير موجودة' },
          { status: 404 }
        )
      }
      console.error('Error fetching page by slug:', error)
      return NextResponse.json(
        { error: 'فشل في جلب الصفحة' },
        { status: 500 }
      )
    }

    // التحقق من صلاحية عرض الصفحة غير المنشورة
    if (!page.is_published) {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        return NextResponse.json(
          { error: 'الصفحة غير متاحة' },
          { status: 404 }
        )
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (!profile || profile.role !== 'admin') {
        return NextResponse.json(
          { error: 'الصفحة غير متاحة' },
          { status: 404 }
        )
      }
    }

    return NextResponse.json({ page })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
