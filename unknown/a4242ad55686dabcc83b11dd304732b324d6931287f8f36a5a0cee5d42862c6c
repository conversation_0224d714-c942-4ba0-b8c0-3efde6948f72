# دليل اختبار Header Menu - Header Menu Testing Guide

## كيفية اختبار التحسينات الجديدة - How to Test New Improvements

### 1. اختبار تبديل اللغات - Language Toggle Testing

#### الخطوات:
1. افتح التطبيق على `http://localhost:3000`
2. ابحث عن زر تبديل اللغات في أعلى يمين الصفحة (يظهر علم المغرب 🇲🇦 و "AR")
3. انقر على الزر لفتح القائمة المنسدلة
4. جرب تغيير اللغة إلى الفرنسية أو الإنجليزية
5. لاحظ التغييرات التالية:
   - تغيير اتجاه النص (RTL ↔ LTR)
   - تغيير النصوص في القائمة
   - تغيير العلم والرمز في الزر
   - تغيير النص الفرعي تحت الشعار

#### ما يجب ملاحظته:
- ✅ التغيير فوري دون إعادة تحميل الصفحة
- ✅ حفظ اللغة المختارة عند إعادة تحميل الصفحة
- ✅ تحديث جميع النصوص في الواجهة
- ✅ تحديث اتجاه النص بشكل صحيح

### 2. اختبار التصميم المحسن - Enhanced Design Testing

#### الخطوات:
1. لاحظ الشعار الجديد مع التأثيرات التفاعلية
2. مرر الماوس فوق عناصر القائمة
3. لاحظ الانتقالات السلسة والتأثيرات البصرية
4. جرب التنقل بين الصفحات المختلفة

#### ما يجب ملاحظته:
- ✅ تأثيرات hover سلسة على جميع العناصر
- ✅ مؤشر الصفحة النشطة واضح
- ✅ خلفية شفافة مع تأثير blur
- ✅ ظلال وألوان محسنة

### 3. اختبار القائمة المحمولة - Mobile Menu Testing

#### الخطوات:
1. قم بتصغير نافذة المتصفح أو استخدم أدوات المطور للمحاكاة المحمولة
2. انقر على زر القائمة (الخطوط الثلاثة)
3. لاحظ الأنيميشن عند فتح وإغلاق القائمة
4. جرب النقر على عناصر القائمة

#### ما يجب ملاحظته:
- ✅ أنيميشن سلس لزر الهامبرغر
- ✅ انتقال سلس لفتح/إغلاق القائمة
- ✅ أنيميشن تدريجي لعناصر القائمة
- ✅ إغلاق تلقائي عند النقر على رابط

### 4. اختبار العدادات - Counters Testing

#### الخطوات:
1. افتح أدوات المطور (F12)
2. اذهب إلى Console
3. أضف عناصر للسلة والمفضلة:
```javascript
// إضافة عناصر للسلة
localStorage.setItem('cartItems', JSON.stringify([{id: 1}, {id: 2}, {id: 3}]))

// إضافة عناصر للمفضلة
localStorage.setItem('wishlistItems', JSON.stringify([{id: 1}, {id: 2}]))

// إعادة تحميل الصفحة
location.reload()
```

#### ما يجب ملاحظته:
- ✅ ظهور عدادات على أيقونات السلة والمفضلة
- ✅ تأثير نبضي للعدادات
- ✅ عرض "99+" للأرقام الكبيرة

### 5. اختبار إمكانية الوصول - Accessibility Testing

#### الخطوات:
1. استخدم Tab للتنقل بين العناصر
2. استخدم Enter/Space لتفعيل الأزرار
3. استخدم قارئ الشاشة إذا كان متاحاً
4. تحقق من تباين الألوان

#### ما يجب ملاحظته:
- ✅ جميع العناصر قابلة للوصول بلوحة المفاتيح
- ✅ نصوص مناسبة للقارئات الصوتية
- ✅ تباين ألوان مناسب
- ✅ مؤشرات focus واضحة

### 6. اختبار الوضع الداكن - Dark Mode Testing

#### الخطوات:
1. انقر على زر تبديل الوضع الداكن
2. لاحظ تغيير الألوان في Header
3. تحقق من وضوح جميع العناصر

#### ما يجب ملاحظته:
- ✅ انتقال سلس بين الأوضاع
- ✅ ألوان مناسبة للوضع الداكن
- ✅ وضوح النصوص والأيقونات
- ✅ تباين مناسب

## اختبار الأداء - Performance Testing

### أدوات الاختبار:
1. **Lighthouse**: لاختبار الأداء العام
2. **React DevTools**: لمراقبة إعادة الرندر
3. **Network Tab**: لمراقبة طلبات الشبكة

### المقاييس المتوقعة:
- ✅ First Contentful Paint < 2s
- ✅ Largest Contentful Paint < 3s
- ✅ Cumulative Layout Shift < 0.1
- ✅ First Input Delay < 100ms

## الإبلاغ عن المشاكل - Bug Reporting

إذا واجهت أي مشاكل، يرجى تسجيلها مع المعلومات التالية:

1. **المتصفح والإصدار**
2. **حجم الشاشة**
3. **الخطوات لإعادة إنتاج المشكلة**
4. **السلوك المتوقع مقابل الفعلي**
5. **لقطات شاشة إذا أمكن**

## الاختبارات التلقائية - Automated Tests

لتشغيل الاختبارات التلقائية:

```bash
cd frontend
npm test
```

### الاختبارات المتاحة:
- `LanguageToggle.test.tsx`: اختبار مكون تبديل اللغات
- `Navigation.test.tsx`: اختبار مكون التنقل الرئيسي

## نصائح للاختبار - Testing Tips

1. **اختبر على متصفحات متعددة**: Chrome, Firefox, Safari, Edge
2. **اختبر على أحجام شاشات مختلفة**: Desktop, Tablet, Mobile
3. **اختبر مع اتصال إنترنت بطيء**: لمحاكاة ظروف حقيقية
4. **اختبر مع JavaScript معطل**: للتأكد من الوصولية الأساسية
5. **اختبر مع قارئ الشاشة**: لضمان إمكانية الوصول
