
# 🎓 Graduation Toqs – First Moroccan Graduation Attire Platform

**Graduation Toqs** is the first smart web platform in Morocco dedicated to the rental and sale of graduation attire, including gowns, toqs (caps), and stoles. The platform is multilingual (Arabic, French, English), AI-enhanced, and offers full attire customization, order tracking, and role-based dashboards for admins, schools, students, and delivery partners.

---

## 🌍 Overview

- 🌐 **Multilingual Support**: Arabic 🇲🇦, French 🇫🇷, English 🇬🇧
- 🧢 **Custom Graduation Design**: Choose styles, colors, and accessories
- 🌓 **Dark/Light Mode**: Fully adaptive UI
- 📦 **Smart Order Tracking**: End-to-end order status updates
- 👥 **Role-Based Dashboards**: Admin, Schools, Students, Delivery
- 🤖 **AI Features**:
  - Chat assistant
  - Smart attire suggestions
  - Auto-generated captions and product descriptions
- 🌍 **Expansion Plan**: Launching in Béni Mellal–Khénifra, scaling nationally

---

## 🚀 Tech Stack

| Layer       | Technology                         |
|------------|-------------------------------------|
| Frontend   | Next.js, TypeScript, Tailwind CSS, Shadcn/UI |
| Backend    | Node.js (NestJS / Express.js)       |
| Database   | Supabase (PostgreSQL)               |
| Auth       | JWT + Supabase Auth (RBAC)          |
| AI Tools   | OpenAI, DeepSeek, LangChain         |
| Translations | i18n with `next-i18next`          |

---

## 🏗️ Project Structure

```
graduation-toqs/
├── frontend/             # Next.js frontend
│   ├── pages/
│   ├── components/
│   ├── features/
│   ├── types/
│   ├── lib/
│   └── locales/          # ar, fr, en
│
├── backend/              # API logic (Node.js)
│   ├── services/
│   ├── ai/
│   ├── controllers/
│   └── guards/
│
├── supabase/             # DB schema & storage setup
├── public/               # Assets and previews
└── docs/                 # Project specs and roadmap
```

---

## 📋 Development Roadmap

### ✅ Phase 1 – Setup & Core
- [x] Next.js + TypeScript + Tailwind CSS
- [x] i18n: Arabic, French, English
- [x] Auth System (JWT or Supabase Auth)
- [x] Light/Dark Mode Toggle

### 🔧 Phase 2 – Features
- [ ] Role-based dashboards (Admin, School, Student, Delivery)
- [ ] Attire customization UI
- [ ] Order placement & tracking
- [ ] Admin product/catalog management

### 🤖 Phase 3 – AI Integration
- [ ] AI Assistant (student support)
- [ ] Smart product recommendations
- [ ] Auto-generated marketing content

### 🌍 Phase 4 – Expansion
- [ ] AR preview for attire (future)
- [ ] Mobile PWA support
- [ ] National partnerships with schools

---

## 🧠 AI Prompt (for Vibe Coding / LLMs)

```
You are building a multilingual Moroccan platform called Graduation Toqs.
Help students personalize their graduation attire (colors, toqs, stoles),
manage orders, and provide smart recommendations using AI.
Support Arabic, French, and English. Dark/light mode required.
```

---

## 📄 License

MIT License – Free to use, modify, and distribute with proper credit.

---

## 🤝 Contact

Created with ❤️ by [Graduation Toqs Team]  
For inquiries or partnerships: <EMAIL>
