// Polyfills for Jest testing environment

// TextEncoder/TextDecoder polyfill
const { TextEncoder, TextDecoder } = require('util')

Object.assign(global, { TextDecoder, TextEncoder })

// Crypto polyfill for Node.js environment
const { webcrypto } = require('crypto')

Object.defineProperty(global, 'crypto', {
  value: webcrypto,
})

// AbortController polyfill
if (!global.AbortController) {
  global.AbortController = class AbortController {
    constructor() {
      this.signal = {
        aborted: false,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }
    }
    
    abort() {
      this.signal.aborted = true
    }
  }
}

// FormData polyfill
if (!global.FormData) {
  global.FormData = class FormData {
    constructor() {
      this.data = new Map()
    }
    
    append(key, value) {
      if (this.data.has(key)) {
        const existing = this.data.get(key)
        if (Array.isArray(existing)) {
          existing.push(value)
        } else {
          this.data.set(key, [existing, value])
        }
      } else {
        this.data.set(key, value)
      }
    }
    
    get(key) {
      const value = this.data.get(key)
      return Array.isArray(value) ? value[0] : value
    }
    
    getAll(key) {
      const value = this.data.get(key)
      return Array.isArray(value) ? value : value ? [value] : []
    }
    
    has(key) {
      return this.data.has(key)
    }
    
    delete(key) {
      this.data.delete(key)
    }
    
    entries() {
      return this.data.entries()
    }
    
    keys() {
      return this.data.keys()
    }
    
    values() {
      return this.data.values()
    }
  }
}

// Headers polyfill
if (!global.Headers) {
  global.Headers = class Headers {
    constructor(init) {
      this.map = new Map()
      if (init) {
        if (init instanceof Headers) {
          for (const [key, value] of init.entries()) {
            this.map.set(key.toLowerCase(), value)
          }
        } else if (Array.isArray(init)) {
          for (const [key, value] of init) {
            this.map.set(key.toLowerCase(), value)
          }
        } else {
          for (const [key, value] of Object.entries(init)) {
            this.map.set(key.toLowerCase(), value)
          }
        }
      }
    }
    
    append(key, value) {
      const existing = this.map.get(key.toLowerCase())
      this.map.set(key.toLowerCase(), existing ? `${existing}, ${value}` : value)
    }
    
    delete(key) {
      this.map.delete(key.toLowerCase())
    }
    
    get(key) {
      return this.map.get(key.toLowerCase()) || null
    }
    
    has(key) {
      return this.map.has(key.toLowerCase())
    }
    
    set(key, value) {
      this.map.set(key.toLowerCase(), value)
    }
    
    entries() {
      return this.map.entries()
    }
    
    keys() {
      return this.map.keys()
    }
    
    values() {
      return this.map.values()
    }
  }
}

// Request polyfill
if (!global.Request) {
  global.Request = class Request {
    constructor(input, init = {}) {
      this.url = typeof input === 'string' ? input : input.url
      this.method = init.method || 'GET'
      this.headers = new Headers(init.headers)
      this.body = init.body || null
      this.mode = init.mode || 'cors'
      this.credentials = init.credentials || 'same-origin'
      this.cache = init.cache || 'default'
      this.redirect = init.redirect || 'follow'
      this.referrer = init.referrer || 'about:client'
      this.integrity = init.integrity || ''
    }
    
    clone() {
      return new Request(this.url, {
        method: this.method,
        headers: this.headers,
        body: this.body,
        mode: this.mode,
        credentials: this.credentials,
        cache: this.cache,
        redirect: this.redirect,
        referrer: this.referrer,
        integrity: this.integrity,
      })
    }
    
    async json() {
      return JSON.parse(this.body || '{}')
    }
    
    async text() {
      return this.body || ''
    }
  }
}

// Response polyfill
if (!global.Response) {
  global.Response = class Response {
    constructor(body, init = {}) {
      this.body = body
      this.status = init.status || 200
      this.statusText = init.statusText || 'OK'
      this.headers = new Headers(init.headers)
      this.ok = this.status >= 200 && this.status < 300
      this.redirected = false
      this.type = 'basic'
      this.url = ''
    }
    
    clone() {
      return new Response(this.body, {
        status: this.status,
        statusText: this.statusText,
        headers: this.headers,
      })
    }
    
    async json() {
      return JSON.parse(this.body || '{}')
    }
    
    async text() {
      return this.body || ''
    }
    
    async blob() {
      return new Blob([this.body || ''])
    }
    
    async arrayBuffer() {
      return new ArrayBuffer(0)
    }
    
    static json(data, init) {
      return new Response(JSON.stringify(data), {
        ...init,
        headers: {
          'Content-Type': 'application/json',
          ...init?.headers,
        },
      })
    }
    
    static error() {
      const response = new Response(null, { status: 0, statusText: '' })
      response.ok = false
      response.type = 'error'
      return response
    }
    
    static redirect(url, status = 302) {
      return new Response(null, {
        status,
        headers: { Location: url },
      })
    }
  }
}

// URLSearchParams polyfill
if (!global.URLSearchParams) {
  global.URLSearchParams = class URLSearchParams {
    constructor(init) {
      this.params = new Map()
      if (typeof init === 'string') {
        this.parseString(init)
      } else if (init instanceof URLSearchParams) {
        for (const [key, value] of init.entries()) {
          this.append(key, value)
        }
      } else if (Array.isArray(init)) {
        for (const [key, value] of init) {
          this.append(key, value)
        }
      } else if (init) {
        for (const [key, value] of Object.entries(init)) {
          this.append(key, value)
        }
      }
    }
    
    parseString(str) {
      const pairs = str.replace(/^\?/, '').split('&')
      for (const pair of pairs) {
        if (pair) {
          const [key, value = ''] = pair.split('=')
          this.append(decodeURIComponent(key), decodeURIComponent(value))
        }
      }
    }
    
    append(key, value) {
      const existing = this.params.get(key)
      if (existing) {
        this.params.set(key, Array.isArray(existing) ? [...existing, value] : [existing, value])
      } else {
        this.params.set(key, value)
      }
    }
    
    delete(key) {
      this.params.delete(key)
    }
    
    get(key) {
      const value = this.params.get(key)
      return Array.isArray(value) ? value[0] : value || null
    }
    
    getAll(key) {
      const value = this.params.get(key)
      return Array.isArray(value) ? value : value ? [value] : []
    }
    
    has(key) {
      return this.params.has(key)
    }
    
    set(key, value) {
      this.params.set(key, value)
    }
    
    entries() {
      const entries = []
      for (const [key, value] of this.params.entries()) {
        if (Array.isArray(value)) {
          for (const v of value) {
            entries.push([key, v])
          }
        } else {
          entries.push([key, value])
        }
      }
      return entries[Symbol.iterator]()
    }
    
    keys() {
      return this.params.keys()
    }
    
    values() {
      const values = []
      for (const value of this.params.values()) {
        if (Array.isArray(value)) {
          values.push(...value)
        } else {
          values.push(value)
        }
      }
      return values[Symbol.iterator]()
    }
    
    toString() {
      const pairs = []
      for (const [key, value] of this.entries()) {
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      }
      return pairs.join('&')
    }
  }
}
