import { render, screen, fireEvent } from '@testing-library/react'
import { Navigation } from '../Navigation'
import { useTranslation } from '@/hooks/useTranslation'
import { useAuth } from '@/contexts/AuthContext'
import { usePathname } from 'next/navigation'

// Mock the hooks
jest.mock('@/hooks/useTranslation')
jest.mock('@/contexts/AuthContext')
jest.mock('next/navigation')

const mockUseTranslation = useTranslation as jest.MockedFunction<typeof useTranslation>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('Navigation', () => {
  beforeEach(() => {
    mockUseTranslation.mockReturnValue({
      locale: 'ar',
      changeLocale: jest.fn(),
      t: (key: string) => {
        const translations: Record<string, string> = {
          'navigation.home': 'الرئيسية',
          'navigation.catalog': 'الكتالوج',
          'navigation.customize': 'التخصيص',
          'navigation.trackOrder': 'تتبع الطلب',
          'navigation.about': 'من نحن',
          'navigation.contact': 'تواصل معنا',
        }
        return translations[key] || key
      },
    })

    mockUseAuth.mockReturnValue({
      user: null,
      profile: null,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
      loading: false,
    })

    mockUsePathname.mockReturnValue('/')

    localStorageMock.getItem.mockReturnValue('[]')
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('renders the logo and brand name', () => {
    render(<Navigation />)
    
    expect(screen.getByText('Graduation Toqs')).toBeInTheDocument()
    expect(screen.getByText('منصة أزياء التخرج')).toBeInTheDocument()
  })

  it('renders all navigation items', () => {
    render(<Navigation />)
    
    expect(screen.getByText('الرئيسية')).toBeInTheDocument()
    expect(screen.getByText('الكتالوج')).toBeInTheDocument()
    expect(screen.getByText('التخصيص')).toBeInTheDocument()
    expect(screen.getByText('تتبع الطلب')).toBeInTheDocument()
    expect(screen.getByText('من نحن')).toBeInTheDocument()
    expect(screen.getByText('تواصل معنا')).toBeInTheDocument()
  })

  it('highlights the active navigation item', () => {
    mockUsePathname.mockReturnValue('/catalog')
    render(<Navigation />)
    
    const catalogLink = screen.getByText('الكتالوج').closest('a')
    expect(catalogLink).toHaveClass('bg-blue-600')
  })

  it('shows cart with item count', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'cartItems') return JSON.stringify([{ id: 1 }, { id: 2 }])
      return '[]'
    })

    render(<Navigation />)
    
    // Should show cart count badge
    expect(screen.getByText('2')).toBeInTheDocument()
  })

  it('shows wishlist with item count', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'wishlistItems') return JSON.stringify([{ id: 1 }])
      return '[]'
    })

    render(<Navigation />)
    
    // Should show wishlist count badge
    expect(screen.getByText('1')).toBeInTheDocument()
  })

  it('opens mobile menu when hamburger is clicked', () => {
    render(<Navigation />)
    
    const mobileMenuButton = screen.getByRole('button', { name: /menu/i })
    fireEvent.click(mobileMenuButton)
    
    // Mobile menu should be visible
    const mobileNav = screen.getByRole('navigation')
    expect(mobileNav).toBeInTheDocument()
  })

  it('closes mobile menu when navigation item is clicked', () => {
    render(<Navigation />)
    
    // Open mobile menu
    const mobileMenuButton = screen.getByRole('button', { name: /menu/i })
    fireEvent.click(mobileMenuButton)
    
    // Click on a navigation item
    const homeLink = screen.getAllByText('الرئيسية')[1] // Get mobile version
    fireEvent.click(homeLink)
    
    // Menu should close (this would need to be tested with proper state management)
  })

  it('displays different brand text based on locale', () => {
    // Test French locale
    mockUseTranslation.mockReturnValue({
      locale: 'fr',
      changeLocale: jest.fn(),
      t: (key: string) => key,
    })

    render(<Navigation />)
    
    expect(screen.getByText('Plateforme de Remise des Diplômes')).toBeInTheDocument()
  })

  it('displays English brand text for English locale', () => {
    // Test English locale
    mockUseTranslation.mockReturnValue({
      locale: 'en',
      changeLocale: jest.fn(),
      t: (key: string) => key,
    })

    render(<Navigation />)
    
    expect(screen.getByText('Graduation Platform')).toBeInTheDocument()
  })

  it('shows dashboard link for authenticated users', () => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      profile: { id: '1', role: 'student', userId: '1' },
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
      loading: false,
    })

    render(<Navigation />)
    
    expect(screen.getByText('لوحة التحكم')).toBeInTheDocument()
  })
})
