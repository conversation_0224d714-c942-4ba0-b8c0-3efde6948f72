"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'

// أنواع الإشعارات
export type NotificationType = 
  | 'order_confirmed'      // تأكيد الطلب
  | 'order_shipped'        // شحن الطلب
  | 'order_delivered'      // تسليم الطلب
  | 'payment_received'     // استلام الدفع
  | 'payment_failed'       // فشل الدفع
  | 'promotion'            // عرض ترويجي
  | 'reminder'             // تذكير
  | 'system'               // إشعار نظام
  | 'message'              // رسالة
  | 'review_request'       // طلب تقييم

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  priority: NotificationPriority
  isRead: boolean
  createdAt: string
  expiresAt?: string
  actionUrl?: string
  actionText?: string
  metadata?: Record<string, any>
  userId: string
}

interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'userId' | 'isRead'>) => void
  markAsRead: (notificationId: string) => void
  markAllAsRead: () => void
  removeNotification: (notificationId: string) => void
  clearAll: () => void
  getNotificationsByType: (type: NotificationType) => Notification[]
  getUnreadNotifications: () => Notification[]
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])

  // تحميل الإشعارات عند تسجيل الدخول
  useEffect(() => {
    if (user) {
      loadNotifications()
      // محاكاة استقبال إشعارات جديدة
      const interval = setInterval(() => {
        // محاكاة إشعار عشوائي كل 30 ثانية للتطوير
        if (Math.random() > 0.8) {
          addMockNotification()
        }
      }, 30000)

      return () => clearInterval(interval)
    }
  }, [user])

  const loadNotifications = () => {
    // محاكاة تحميل الإشعارات من الخادم
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'order_confirmed',
        title: 'تم تأكيد طلبك',
        message: 'تم تأكيد طلبك #GT-240120-001 بنجاح وسيتم تحضيره قريباً',
        priority: 'high',
        isRead: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        actionUrl: '/track-order',
        actionText: 'تتبع الطلب',
        userId: user?.id || '',
        metadata: { orderId: 'GT-240120-001' }
      },
      {
        id: '2',
        type: 'promotion',
        title: 'عرض خاص - خصم 20%',
        message: 'احصل على خصم 20% على جميع أزياء التخرج لفترة محدودة',
        priority: 'medium',
        isRead: false,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        actionUrl: '/catalog',
        actionText: 'تسوق الآن',
        userId: user?.id || '',
        metadata: { promoCode: 'GRAD20' }
      },
      {
        id: '3',
        type: 'order_shipped',
        title: 'تم شحن طلبك',
        message: 'طلبك #GT-240115-002 في طريقه إليك. رقم التتبع: TRK-123456',
        priority: 'high',
        isRead: true,
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        actionUrl: '/track-order',
        actionText: 'تتبع الشحنة',
        userId: user?.id || '',
        metadata: { orderId: 'GT-240115-002', trackingNumber: 'TRK-123456' }
      },
      {
        id: '4',
        type: 'review_request',
        title: 'قيم تجربتك معنا',
        message: 'نود معرفة رأيك في المنتجات التي استلمتها مؤخراً',
        priority: 'low',
        isRead: false,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        actionUrl: '/reviews',
        actionText: 'اكتب تقييم',
        userId: user?.id || ''
      }
    ]

    setNotifications(mockNotifications)
  }

  const addMockNotification = () => {
    const mockTypes: NotificationType[] = ['system', 'promotion', 'reminder']
    const randomType = mockTypes[Math.floor(Math.random() * mockTypes.length)]
    
    const mockMessages = {
      system: {
        title: 'تحديث النظام',
        message: 'تم تحديث النظام بميزات جديدة'
      },
      promotion: {
        title: 'عرض محدود',
        message: 'خصم خاص على المنتجات المختارة'
      },
      reminder: {
        title: 'تذكير',
        message: 'لا تنس إكمال طلبك في سلة التسوق'
      }
    }

    addNotification({
      type: randomType,
      title: mockMessages[randomType].title,
      message: mockMessages[randomType].message,
      priority: 'medium'
    })
  }

  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt' | 'userId' | 'isRead'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      userId: user?.id || '',
      isRead: false
    }

    setNotifications(prev => [newNotification, ...prev])

    // إظهار إشعار المتصفح إذا كان مسموحاً
    if (Notification.permission === 'granted') {
      new Notification(newNotification.title, {
        body: newNotification.message,
        icon: '/favicon.ico',
        tag: newNotification.id
      })
    }
  }

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, isRead: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    )
  }

  const removeNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    )
  }

  const clearAll = () => {
    setNotifications([])
  }

  const getNotificationsByType = (type: NotificationType) => {
    return notifications.filter(notification => notification.type === type)
  }

  const getUnreadNotifications = () => {
    return notifications.filter(notification => !notification.isRead)
  }

  const unreadCount = getUnreadNotifications().length

  // طلب إذن الإشعارات عند التحميل
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    getNotificationsByType,
    getUnreadNotifications
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

// دوال مساعدة
export function getNotificationIcon(type: NotificationType): string {
  switch (type) {
    case 'order_confirmed':
    case 'order_shipped':
    case 'order_delivered':
      return '📦'
    case 'payment_received':
      return '💳'
    case 'payment_failed':
      return '❌'
    case 'promotion':
      return '🎉'
    case 'reminder':
      return '⏰'
    case 'system':
      return '⚙️'
    case 'message':
      return '💬'
    case 'review_request':
      return '⭐'
    default:
      return '🔔'
  }
}

export function getNotificationColor(priority: NotificationPriority): string {
  switch (priority) {
    case 'urgent':
      return 'text-red-600 bg-red-50 border-red-200'
    case 'high':
      return 'text-orange-600 bg-orange-50 border-orange-200'
    case 'medium':
      return 'text-blue-600 bg-blue-50 border-blue-200'
    case 'low':
      return 'text-gray-600 bg-gray-50 border-gray-200'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200'
  }
}

export function formatNotificationTime(timestamp: string): string {
  const now = new Date()
  const notificationTime = new Date(timestamp)
  const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) {
    return 'الآن'
  } else if (diffInMinutes < 60) {
    return `منذ ${diffInMinutes} دقيقة`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `منذ ${hours} ساعة`
  } else {
    const days = Math.floor(diffInMinutes / 1440)
    return `منذ ${days} يوم`
  }
}
