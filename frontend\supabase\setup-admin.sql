-- ملف إعداد مدير الموقع
-- يجب تشغيل هذا الملف بعد إنشاء المستخدم في Supabase Dashboard

-- التحقق من وجود المستخدم في auth.users وربطه بالملف التعريفي
DO $$
DECLARE
  admin_auth_id uuid;
  admin_profile_id uuid;
  admin_profile_exists boolean;
BEGIN
  -- البحث عن معرف المستخدم في جدول المصادقة
  SELECT id INTO admin_auth_id 
  FROM auth.users 
  WHERE email = '<EMAIL>';
  
  IF admin_auth_id IS NULL THEN
    RAISE EXCEPTION 'المستخدم <EMAIL> غير موجود في جدول auth.users. يرجى إنشاؤه أولاً من Supabase Dashboard.';
  END IF;
  
  -- البحث عن الملف التعريفي
  SELECT id INTO admin_profile_id
  FROM profiles 
  WHERE email = '<EMAIL>';
  
  IF admin_profile_id IS NULL THEN
    -- إنشاء ملف تعريفي جديد
    INSERT INTO profiles (
      id,
      email,
      full_name,
      role,
      phone,
      country,
      is_active,
      created_at,
      updated_at
    ) VALUES (
      admin_auth_id,
      '<EMAIL>',
      'مدير الموقع الرئيسي',
      'admin',
      '+212 5XX-XXXXXX',
      'Morocco',
      true,
      NOW(),
      NOW()
    );
    
    RAISE NOTICE 'تم إنشاء ملف تعريفي جديد للمدير';
  ELSE
    -- تحديث الملف التعريفي الموجود
    UPDATE profiles 
    SET 
      id = admin_auth_id,
      role = 'admin',
      full_name = 'مدير الموقع الرئيسي',
      phone = COALESCE(phone, '+212 5XX-XXXXXX'),
      country = COALESCE(country, 'Morocco'),
      is_active = true,
      updated_at = NOW()
    WHERE email = '<EMAIL>';
    
    RAISE NOTICE 'تم تحديث الملف التعريفي للمدير وربطه بالمستخدم';
  END IF;
  
  RAISE NOTICE 'معرف المدير: %', admin_auth_id;
  RAISE NOTICE 'يمكنك الآن تسجيل الدخول باستخدام: <EMAIL>';
END $$;

-- التحقق من النتيجة النهائية
SELECT 
  'معلومات المدير بعد الإعداد:' as status,
  p.id,
  p.email,
  p.full_name,
  p.role,
  p.phone,
  p.country,
  p.is_active,
  p.created_at,
  CASE 
    WHEN au.id IS NOT NULL THEN 'مرتبط بنجاح'
    ELSE 'غير مرتبط'
  END as auth_status,
  au.email_confirmed_at,
  au.created_at as auth_created_at
FROM profiles p
LEFT JOIN auth.users au ON p.id = au.id
WHERE p.email = '<EMAIL>';

-- إنشاء إشعار ترحيب للمدير
INSERT INTO notifications (
  user_id,
  title,
  title_en,
  title_fr,
  message,
  message_en,
  message_fr,
  type,
  action_url,
  created_at
) 
SELECT 
  p.id,
  'مرحباً بك في منصة أزياء التخرج!',
  'Welcome to Graduation Toqs Platform!',
  'Bienvenue sur la Plateforme Toqs de Graduation!',
  'تم إعداد حسابك كمدير بنجاح. يمكنك الآن الوصول إلى جميع أدوات الإدارة وإدارة المنصة.',
  'Your account has been successfully set up as an administrator. You can now access all management tools and manage the platform.',
  'Votre compte a été configuré avec succès en tant qu''administrateur. Vous pouvez maintenant accéder à tous les outils de gestion et gérer la plateforme.',
  'success',
  '/dashboard/admin',
  NOW()
FROM profiles p
WHERE p.email = '<EMAIL>'
AND NOT EXISTS (
  SELECT 1 FROM notifications 
  WHERE user_id = p.id AND title = 'مرحباً بك في منصة أزياء التخرج!'
);

-- إضافة عنصر قائمة للوحة الإدارة (إذا لم يكن موجوداً)
INSERT INTO menu_items (
  title_ar, 
  title_en, 
  title_fr, 
  slug, 
  icon, 
  order_index, 
  target_type, 
  target_value,
  permissions
) VALUES (
  'لوحة الإدارة',
  'Admin Panel',
  'Panneau Admin',
  'admin-panel',
  'Settings',
  7,
  'internal',
  '/dashboard/admin',
  ARRAY['admin']
)
ON CONFLICT (slug) DO UPDATE SET
  title_ar = EXCLUDED.title_ar,
  title_en = EXCLUDED.title_en,
  title_fr = EXCLUDED.title_fr,
  permissions = EXCLUDED.permissions,
  updated_at = NOW();

-- عرض ملخص الإعداد النهائي
SELECT 
  'تم إعداد المدير بنجاح!' as final_status,
  '<EMAIL>' as admin_email,
  'يمكنك الآن تسجيل الدخول والوصول إلى:' as access_info,
  '/dashboard/admin' as admin_dashboard,
  '/dashboard/admin/menu-management' as menu_management,
  '/dashboard/admin/pages-management' as pages_management;

-- تعليمات الاستخدام
SELECT 
  'تعليمات الاستخدام:' as instructions,
  '1. سجل دخول باستخدام <EMAIL>' as step_1,
  '2. اذهب إلى /dashboard/admin' as step_2,
  '3. استكشف أدوات الإدارة المتاحة' as step_3,
  '4. ابدأ بإدارة القائمة والصفحات' as step_4;
