{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport function createClient(cookieStore: ReturnType<typeof cookies>) {\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          try {\n            cookieStore.set({ name, value, ...options })\n          } catch (error) {\n            // The `set` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n        remove(name: string, options: any) {\n          try {\n            cookieStore.set({ name, value: '', ...options })\n          } catch (error) {\n            // The `delete` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS,aAAa,WAAuC;IAClE,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM;wBAAO,GAAG,OAAO;oBAAC;gBAC5C,EAAE,OAAO,OAAO;gBACd,uDAAuD;gBACvD,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM,OAAO;wBAAI,GAAG,OAAO;oBAAC;gBAChD,EAAE,OAAO,OAAO;gBACd,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { cookies } from 'next/headers'\n\n// GET - جلب جميع المنتجات\nexport async function GET(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    const { searchParams } = new URL(request.url)\n    const category = searchParams.get('category')\n    const available = searchParams.get('available')\n    const limit = searchParams.get('limit')\n    const offset = searchParams.get('offset')\n\n    let query = supabase\n      .from('products')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    // تطبيق الفلاتر\n    if (category && category !== 'all') {\n      query = query.eq('category', category)\n    }\n\n    if (available === 'true') {\n      query = query.eq('is_available', true)\n    } else if (available === 'false') {\n      query = query.eq('is_available', false)\n    }\n\n    // تطبيق التصفح\n    if (limit) {\n      query = query.limit(parseInt(limit))\n    }\n\n    if (offset) {\n      query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10') - 1))\n    }\n\n    const { data: products, error } = await query\n\n    if (error) {\n      console.error('Error fetching products:', error)\n      return NextResponse.json(\n        { error: 'فشل في جلب المنتجات' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ products })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة منتج جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات المستخدم\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    // التحقق من دور المستخدم\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بإضافة المنتجات' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    const {\n      name,\n      description,\n      category,\n      price,\n      rental_price,\n      colors,\n      sizes,\n      images,\n      stock_quantity,\n      is_available,\n      features,\n      specifications\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!name || !description || !category || !price || !colors || !sizes) {\n      return NextResponse.json(\n        { error: 'البيانات المطلوبة مفقودة' },\n        { status: 400 }\n      )\n    }\n\n    // إدراج المنتج في قاعدة البيانات\n    const { data: product, error: insertError } = await supabase\n      .from('products')\n      .insert({\n        name,\n        description,\n        category,\n        price: parseFloat(price),\n        rental_price: rental_price ? parseFloat(rental_price) : null,\n        colors,\n        sizes,\n        images: images || [],\n        stock_quantity: parseInt(stock_quantity) || 0,\n        is_available: is_available ?? true,\n        features: features || [],\n        specifications: specifications || {}\n      })\n      .select()\n      .single()\n\n    if (insertError) {\n      console.error('Error inserting product:', insertError)\n      return NextResponse.json(\n        { error: 'فشل في إضافة المنتج' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ \n      message: 'تم إضافة المنتج بنجاح',\n      product \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - تحديث منتج موجود\nexport async function PUT(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات المستخدم\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    // التحقق من دور المستخدم\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بتحديث المنتجات' },\n        { status: 403 }\n      )\n    }\n\n    const body = await request.json()\n    const { id, ...updateData } = body\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'معرف المنتج مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    // تحديث المنتج\n    const { data: product, error: updateError } = await supabase\n      .from('products')\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (updateError) {\n      console.error('Error updating product:', updateError)\n      return NextResponse.json(\n        { error: 'فشل في تحديث المنتج' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ \n      message: 'تم تحديث المنتج بنجاح',\n      product \n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - حذف منتج\nexport async function DELETE(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات المستخدم\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    // التحقق من دور المستخدم\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بحذف المنتجات' },\n        { status: 403 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const id = searchParams.get('id')\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'معرف المنتج مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    // حذف المنتج\n    const { error: deleteError } = await supabase\n      .from('products')\n      .delete()\n      .eq('id', id)\n\n    if (deleteError) {\n      console.error('Error deleting product:', deleteError)\n      return NextResponse.json(\n        { error: 'فشل في حذف المنتج' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ \n      message: 'تم حذف المنتج بنجاح'\n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,gBAAgB;QAChB,IAAI,YAAY,aAAa,OAAO;YAClC,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QAEA,IAAI,cAAc,QAAQ;YACxB,QAAQ,MAAM,EAAE,CAAC,gBAAgB;QACnC,OAAO,IAAI,cAAc,SAAS;YAChC,QAAQ,MAAM,EAAE,CAAC,gBAAgB;QACnC;QAEA,eAAe;QACf,IAAI,OAAO;YACT,QAAQ,MAAM,KAAK,CAAC,SAAS;QAC/B;QAEA,IAAI,QAAQ;YACV,QAAQ,MAAM,KAAK,CAAC,SAAS,SAAS,SAAS,UAAU,CAAC,SAAS,SAAS,QAAQ,CAAC;QACvF;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM;QAExC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAS;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,6BAA6B;QAC7B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,MAAM,EACN,KAAK,EACL,MAAM,EACN,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,cAAc,EACf,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO;YACrE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,YACL,MAAM,CAAC;YACN;YACA;YACA;YACA,OAAO,WAAW;YAClB,cAAc,eAAe,WAAW,gBAAgB;YACxD;YACA;YACA,QAAQ,UAAU,EAAE;YACpB,gBAAgB,SAAS,mBAAmB;YAC5C,cAAc,gBAAgB;YAC9B,UAAU,YAAY,EAAE;YACxB,gBAAgB,kBAAkB,CAAC;QACrC,GACC,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,6BAA6B;QAC7B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,YACL,MAAM,CAAC;YACN,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,6BAA6B;QAC7B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}