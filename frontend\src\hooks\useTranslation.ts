"use client"

import { useState, useEffect } from 'react';
import { Locale, defaultLocale } from '@/lib/i18n';

// Import translation files
import arTranslations from '@/locales/ar.json';
import frTranslations from '@/locales/fr.json';
import enTranslations from '@/locales/en.json';

const translations = {
  ar: arTranslations,
  fr: frTranslations,
  en: enTranslations,
};

export function useTranslation() {
  const [locale, setLocale] = useState<Locale>(defaultLocale);

  useEffect(() => {
    // Get locale from localStorage or use default
    const savedLocale = localStorage.getItem('locale') as Locale;
    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {
      setLocale(savedLocale);
    }
  }, []);

  const changeLocale = (newLocale: Locale) => {
    setLocale(newLocale);
    localStorage.setItem('locale', newLocale);
    
    // Update document direction and language
    document.documentElement.lang = newLocale;
    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';
  };

  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = translations[locale];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  return {
    locale,
    changeLocale,
    t,
  };
}
