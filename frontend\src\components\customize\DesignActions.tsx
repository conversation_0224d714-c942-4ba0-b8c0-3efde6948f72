"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Save,
  Share2,
  Download,
  Heart,
  Copy,
  Facebook,
  Twitter,
  Instagram,
  MessageCircle,
  Mail,
  Link,
  QrCode,
  Printer
} from 'lucide-react'

interface DesignActionsProps {
  designData: any
  designName?: string
  onSave?: (name: string, description: string) => void
  onShare?: (platform: string) => void
  className?: string
}

export function DesignActions({
  designData,
  designName = "تصميمي المخصص",
  onSave,
  onShare,
  className = ""
}: DesignActionsProps) {
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [shareDialogOpen, setShareDialogOpen] = useState(false)
  const [designTitle, setDesignTitle] = useState(designName)
  const [designDescription, setDesignDescription] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [shareUrl] = useState(`https://graduation-toqs.com/design/${Date.now()}`)

  const handleSave = async () => {
    if (!designTitle.trim()) return
    
    setIsSaving(true)
    try {
      await onSave?.(designTitle, designDescription)
      setSaveDialogOpen(false)
      // Show success message
    } catch (error) {
      // Show error message
    } finally {
      setIsSaving(false)
    }
  }

  const handleShare = (platform: string) => {
    onShare?.(platform)
    
    const text = `شاهد تصميم زي التخرج المخصص الخاص بي على Graduation Toqs!`
    const url = shareUrl

    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank')
        break
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank')
        break
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`, '_blank')
        break
      case 'copy':
        navigator.clipboard.writeText(url)
        break
    }
  }

  const handleDownload = (format: string) => {
    // Implement download functionality
    console.log(`Downloading design as ${format}`)
  }

  const handlePrint = () => {
    window.print()
  }

  const shareOptions = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: <Facebook className="h-5 w-5" />,
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: <Twitter className="h-5 w-5" />,
      color: 'bg-sky-500 hover:bg-sky-600'
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: <Instagram className="h-5 w-5" />,
      color: 'bg-pink-600 hover:bg-pink-700'
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: <MessageCircle className="h-5 w-5" />,
      color: 'bg-green-600 hover:bg-green-700'
    }
  ]

  const downloadOptions = [
    { format: 'png', name: 'صورة PNG', description: 'جودة عالية للطباعة' },
    { format: 'jpg', name: 'صورة JPG', description: 'حجم أصغر للمشاركة' },
    { format: 'pdf', name: 'ملف PDF', description: 'للطباعة الاحترافية' },
    { format: 'svg', name: 'ملف SVG', description: 'قابل للتحرير' }
  ]

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
        {/* Save Design */}
        <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="arabic-text">
              <Save className="h-4 w-4 mr-2" />
              حفظ التصميم
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="arabic-text">حفظ التصميم</DialogTitle>
              <DialogDescription className="arabic-text">
                احفظ تصميمك المخصص لتتمكن من الوصول إليه لاحقاً
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="design-title" className="arabic-text">اسم التصميم</Label>
                <Input
                  id="design-title"
                  value={designTitle}
                  onChange={(e) => setDesignTitle(e.target.value)}
                  placeholder="أدخل اسم التصميم"
                  className="arabic-text"
                />
              </div>
              <div>
                <Label htmlFor="design-description" className="arabic-text">وصف التصميم (اختياري)</Label>
                <Textarea
                  id="design-description"
                  value={designDescription}
                  onChange={(e) => setDesignDescription(e.target.value)}
                  placeholder="أضف وصفاً لتصميمك..."
                  className="arabic-text"
                  rows={3}
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={handleSave} 
                  disabled={!designTitle.trim() || isSaving}
                  className="flex-1 arabic-text"
                >
                  {isSaving ? 'جاري الحفظ...' : 'حفظ'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setSaveDialogOpen(false)}
                  className="arabic-text"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Share Design */}
        <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="arabic-text">
              <Share2 className="h-4 w-4 mr-2" />
              مشاركة
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="arabic-text">مشاركة التصميم</DialogTitle>
              <DialogDescription className="arabic-text">
                شارك تصميمك المميز مع الأصدقاء والعائلة
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {/* Share URL */}
              <div>
                <Label className="arabic-text">رابط التصميم</Label>
                <div className="flex gap-2">
                  <Input value={shareUrl} readOnly className="flex-1" />
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleShare('copy')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Social Media Buttons */}
              <div>
                <Label className="arabic-text mb-3 block">مشاركة على وسائل التواصل</Label>
                <div className="grid grid-cols-2 gap-3">
                  {shareOptions.map((option) => (
                    <Button
                      key={option.id}
                      variant="outline"
                      onClick={() => handleShare(option.id)}
                      className={`${option.color} text-white border-0 arabic-text`}
                    >
                      {option.icon}
                      <span className="mr-2">{option.name}</span>
                    </Button>
                  ))}
                </div>
              </div>

              {/* QR Code */}
              <div className="text-center">
                <div className="w-32 h-32 bg-gray-100 dark:bg-gray-800 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <QrCode className="h-16 w-16 text-gray-400" />
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  رمز QR للمشاركة السريعة
                </p>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Download Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg arabic-text">تحميل التصميم</CardTitle>
          <CardDescription className="arabic-text">
            احصل على نسخة من تصميمك بصيغ مختلفة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-3">
            {downloadOptions.map((option) => (
              <button
                key={option.format}
                onClick={() => handleDownload(option.format)}
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="text-left">
                  <div className="font-medium arabic-text">{option.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                    {option.description}
                  </div>
                </div>
                <Download className="h-5 w-5 text-gray-400" />
              </button>
            ))}
          </div>

          <Button 
            variant="outline" 
            onClick={handlePrint}
            className="w-full mt-4 arabic-text"
          >
            <Printer className="h-4 w-4 mr-2" />
            طباعة التصميم
          </Button>
        </CardContent>
      </Card>

      {/* Design Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg arabic-text">إحصائيات التصميم</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">12</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">مشاهدة</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">3</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إعجاب</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add to Favorites */}
      <Button variant="outline" className="w-full arabic-text">
        <Heart className="h-4 w-4 mr-2" />
        إضافة للمفضلة
      </Button>
    </div>
  )
}
