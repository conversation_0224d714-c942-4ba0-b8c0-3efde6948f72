import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockMenuItem } from '@/lib/mockData'

// GET - جلب عنصر قائمة واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const menuItems = MockDataManager.getMenuItems()
    const menuItem = menuItems.find(item => item.id === params.id)

    if (!menuItem) {
      return NextResponse.json(
        { error: 'عنصر القائمة غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({ menuItem })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث عنصر قائمة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      title_ar,
      title_en,
      title_fr,
      slug,
      icon,
      parent_id,
      order_index,
      is_active,
      target_type,
      target_value
    } = body

    // جلب العناصر الحالية
    const menuItems = MockDataManager.getMenuItems()
    const itemIndex = menuItems.findIndex(item => item.id === params.id)

    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'عنصر القائمة غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار الـ slug (إذا تم تغييره)
    if (slug && slug !== menuItems[itemIndex].slug) {
      const existingItem = menuItems.find(item => item.slug === slug && item.id !== params.id)
      if (existingItem) {
        return NextResponse.json(
          { error: 'الرابط المختصر موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // تحديث العنصر
    const updatedItem: MockMenuItem = {
      ...menuItems[itemIndex],
      title_ar: title_ar || menuItems[itemIndex].title_ar,
      title_en: title_en || menuItems[itemIndex].title_en,
      title_fr: title_fr || menuItems[itemIndex].title_fr,
      slug: slug || menuItems[itemIndex].slug,
      icon: icon !== undefined ? icon : menuItems[itemIndex].icon,
      parent_id: parent_id !== undefined ? parent_id : menuItems[itemIndex].parent_id,
      order_index: order_index !== undefined ? order_index : menuItems[itemIndex].order_index,
      is_active: is_active !== undefined ? is_active : menuItems[itemIndex].is_active,
      target_type: target_type || menuItems[itemIndex].target_type,
      target_value: target_value || menuItems[itemIndex].target_value,
      updated_at: new Date().toISOString()
    }

    // حفظ التحديثات
    menuItems[itemIndex] = updatedItem
    MockDataManager.saveMenuItems(menuItems)

    return NextResponse.json({ 
      message: 'تم تحديث عنصر القائمة بنجاح',
      menuItem: updatedItem 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف عنصر قائمة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // جلب العناصر الحالية
    const menuItems = MockDataManager.getMenuItems()
    const itemIndex = menuItems.findIndex(item => item.id === params.id)

    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'عنصر القائمة غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من وجود عناصر فرعية
    const childItems = menuItems.filter(item => item.parent_id === params.id)
    if (childItems.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف عنصر يحتوي على عناصر فرعية' },
        { status: 400 }
      )
    }

    // حذف العنصر
    menuItems.splice(itemIndex, 1)
    MockDataManager.saveMenuItems(menuItems)

    return NextResponse.json({ 
      message: 'تم حذف عنصر القائمة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
